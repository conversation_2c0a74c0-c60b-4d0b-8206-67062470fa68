"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var dom_1 = require("@testing-library/dom");
require("@testing-library/jest-dom/vitest");
var vitest_1 = require("vitest");
var frontends_i18n_1 = require("frontends-i18n");
var i18n_json_1 = require("./i18n.json");
global.window.__hudlEmbed = {
    data: {
        i18nMessages: __assign({}, i18n_json_1.default['base-language']),
    },
};
beforeAll(function () {
    // Set up i18n translations
    (0, frontends_i18n_1.setIntl)({ locale: 'en', messages: global.window.__hudlEmbed.data.i18nMessages });
});
// Sets test ID to 'data-qa-id' globally in ticketing app
(0, dom_1.configure)({ testIdAttribute: 'data-qa-id' });
vitest_1.vi.mock('@hudl/frontends-logging');
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vitest_1.vi.fn().mockImplementation(function (query) { return ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vitest_1.vi.fn(), // deprecated
        removeListener: vitest_1.vi.fn(), // deprecated
        addEventListener: vitest_1.vi.fn(),
        removeEventListener: vitest_1.vi.fn(),
        dispatchEvent: vitest_1.vi.fn(),
    }); }),
});
