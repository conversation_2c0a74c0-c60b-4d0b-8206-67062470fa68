"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.aTicketTypeReference = exports.aFormField = exports.aVenueConfiguration = exports.aSeatingChart = exports.aSeatsDotIoCategory = exports.aTicketType = exports.aTicketedEvent = void 0;
var sharedEnums_1 = require("../enums/sharedEnums");
var aTicketedEvent = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ id: 'test-id', name: 'test-name', description: 'test-description', gender: 'women', eventStatus: sharedEnums_1.TicketedEventStatus.Draft, date: '2024-09-16T19:33:59.763Z', feeStrategy: sharedEnums_1.FeeStrategy.PaidByCustomer, formFieldIds: [], ticketTypeReferences: [], ticketTypes: [], timezoneIdentifier: 'America/Chicago', venueId: 'test-venue-id', visibility: sharedEnums_1.TicketingEntityVisibility.Public, participatingTeamIds: [] }, overrides);
};
exports.aTicketedEvent = aTicketedEvent;
var aTicketType = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ id: 'test-id', name: 'test-name', organizationId: 'test-organization-id', priceInCents: 1000, updatedAt: '2024-09-16T19:33:59.763Z', createdAt: '2024-09-16T19:33:59.763Z' }, overrides);
};
exports.aTicketType = aTicketType;
var aSeatsDotIoCategory = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ label: 'test-label', accessible: false, color: 'test-color', key: 'test-key' }, overrides);
};
exports.aSeatsDotIoCategory = aSeatsDotIoCategory;
var aSeatingChart = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ id: 'test-id', seatingChartId: 'test-seating-chart-id', seatingChartProvider: 'test-seating-chart-provider', seatsDotIoCategories: [], seatsDotIoParentSeasonId: 'test-seats-dot-io-parent-season-id', workspace: 'test-workspace' }, overrides);
};
exports.aSeatingChart = aSeatingChart;
var aVenueConfiguration = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ id: 'test-id', description: 'test-description', name: 'test-name', organizationIds: [], seatingChartId: 'test-seating-chart-id', venueId: 'test-venue-id', seatingChart: (0, exports.aSeatingChart)() }, overrides);
};
exports.aVenueConfiguration = aVenueConfiguration;
var aFormField = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ id: 'test-id', label: 'test-label', fieldType: 'test-field-type', isRequired: false, organizationId: 'test-organization-id', createdAt: '2024-09-16T19:33:59.763Z', updatedAt: '2024-09-16T19:33:59.763Z' }, overrides);
};
exports.aFormField = aFormField;
var aTicketTypeReference = function (overrides) {
    if (overrides === void 0) { overrides = {}; }
    return __assign({ ticketTypeId: 'test-ticket-type-id', priceOverride: 1000, quantityOverride: 10, seatingChartCategoryIds: [] }, overrides);
};
exports.aTicketTypeReference = aTicketTypeReference;
