"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Currency = exports.PassConfigVisibility = exports.PayoutStatus = exports.PayoutType = exports.LinkedEntryType = exports.FormFieldType = exports.FormFieldSortType = exports.TicketedEventStatus = exports.TicketingEntityVisibility = exports.TicketingEntityType = exports.FeeStrategy = void 0;
var FeeStrategy;
(function (FeeStrategy) {
    FeeStrategy["PaidByOrganization"] = "PaidByOrganization";
    FeeStrategy["PaidByCustomer"] = "PaidByCustomer";
    FeeStrategy["ProcessingPaidByOrganization"] = "ProcessingPaidByOrganization";
    FeeStrategy["Unknown"] = "Unknown";
})(FeeStrategy || (exports.FeeStrategy = FeeStrategy = {}));
var TicketingEntityType;
(function (TicketingEntityType) {
    TicketingEntityType["Ticket"] = "Ticket";
    TicketingEntityType["Pass"] = "Pass";
})(TicketingEntityType || (exports.TicketingEntityType = TicketingEntityType = {}));
var TicketingEntityVisibility;
(function (TicketingEntityVisibility) {
    TicketingEntityVisibility["Public"] = "Public";
    TicketingEntityVisibility["Private"] = "Private";
    TicketingEntityVisibility["NotForSale"] = "NotForSale";
})(TicketingEntityVisibility || (exports.TicketingEntityVisibility = TicketingEntityVisibility = {}));
var TicketedEventStatus;
(function (TicketedEventStatus) {
    TicketedEventStatus["Upcoming"] = "Upcoming";
    TicketedEventStatus["Draft"] = "Draft";
})(TicketedEventStatus || (exports.TicketedEventStatus = TicketedEventStatus = {}));
var FormFieldSortType;
(function (FormFieldSortType) {
    FormFieldSortType["CreatedAt"] = "CreatedAt";
})(FormFieldSortType || (exports.FormFieldSortType = FormFieldSortType = {}));
var FormFieldType;
(function (FormFieldType) {
    FormFieldType["Text"] = "Text";
})(FormFieldType || (exports.FormFieldType = FormFieldType = {}));
var LinkedEntryType;
(function (LinkedEntryType) {
    LinkedEntryType["HudlScheduleEntry"] = "HudlScheduleEntry";
})(LinkedEntryType || (exports.LinkedEntryType = LinkedEntryType = {}));
var PayoutType;
(function (PayoutType) {
    PayoutType["Check"] = "Check";
    PayoutType["DirectDeposit"] = "DirectDeposit";
    PayoutType["Unknown"] = "Unknown";
})(PayoutType || (exports.PayoutType = PayoutType = {}));
var PayoutStatus;
(function (PayoutStatus) {
    PayoutStatus["Unknown"] = "Unknown";
    PayoutStatus["Incomplete"] = "Incomplete";
    PayoutStatus["NeedsReview"] = "NeedsReview";
    PayoutStatus["UnderExternalReview"] = "UnderExternalReview";
    PayoutStatus["Complete"] = "Complete";
})(PayoutStatus || (exports.PayoutStatus = PayoutStatus = {}));
var PassConfigVisibility;
(function (PassConfigVisibility) {
    PassConfigVisibility["Public"] = "Public";
    PassConfigVisibility["Private"] = "Private";
    PassConfigVisibility["NotForSale"] = "NotForSale";
    PassConfigVisibility["Renewal"] = "Renewal";
})(PassConfigVisibility || (exports.PassConfigVisibility = PassConfigVisibility = {}));
var Currency;
(function (Currency) {
    Currency["USD"] = "USD";
    Currency["CAD"] = "CAD";
    Currency["EUR"] = "EUR";
})(Currency || (exports.Currency = Currency = {}));
