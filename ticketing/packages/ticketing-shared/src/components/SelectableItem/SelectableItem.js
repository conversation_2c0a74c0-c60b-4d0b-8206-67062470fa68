"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SelectableItem = SelectableItem;
var uniform_web_1 = require("@hudl/uniform-web");
var qaIdUtils_1 = require("../../components/utils/qaIdUtils");
var SelectableItem_module_scss_1 = require("./SelectableItem.module.scss");
function SelectableItem(props) {
    var icon = props.icon, title = props.title, description = props.description, isSelected = props.isSelected, onClick = props.onClick;
    var onSelectableItemClick = function () {
        onClick();
    };
    return (<div className={isSelected ? SelectableItem_module_scss_1.default.selectableItemSelected : SelectableItem_module_scss_1.default.selectableItem} onClick={onSelectableItemClick} onKeyDown={onSelectableItemClick} role="button" tabIndex={0} data-qa-id={"selectable-item-".concat((0, qaIdUtils_1.kebabLowercaseQaId)(title))}>
      <div>{icon}</div>
      <div>
        <uniform_web_1.Text>
          <b>{title}</b>
        </uniform_web_1.Text>
      </div>
      <div className={SelectableItem_module_scss_1.default.descriptionText}>
        <uniform_web_1.Text>{description}</uniform_web_1.Text>
      </div>
    </div>);
}
