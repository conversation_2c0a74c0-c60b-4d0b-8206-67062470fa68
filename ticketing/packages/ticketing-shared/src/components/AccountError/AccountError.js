"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AccountError = AccountError;
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_button_legacy_1 = require("@hudl/uniform-web-button-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var AccountError_module_scss_1 = require("./AccountError.module.scss");
function AccountError(props) {
    var qaId = props.qaId;
    var reloadPage = function () {
        window.location.reload();
    };
    return (<div data-qa-id={qaId} className={AccountError_module_scss_1.default.errorAndLoadingContainer}>
      <uniform_web_1.Note type="critical" size="large" className={AccountError_module_scss_1.default.errorNote}>
        {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.onboarding.error-fetching-account-status' })}
      </uniform_web_1.Note>
      <uniform_web_button_legacy_1.Button buttonType="subtle" onClick={reloadPage} qaId="account-error-retry-button">
        {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.refresh' })}
      </uniform_web_button_legacy_1.Button>
    </div>);
}
