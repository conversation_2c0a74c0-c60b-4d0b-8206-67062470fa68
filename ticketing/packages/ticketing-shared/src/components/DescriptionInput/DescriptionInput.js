"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DescriptionInput = DescriptionInput;
var uniform_web_1 = require("@hudl/uniform-web");
var frontends_i18n_1 = require("frontends-i18n");
var sharedEnums_1 = require("../../enums/sharedEnums");
function DescriptionInput(props) {
    var description = props.description, setDescription = props.setDescription, setFormModified = props.setFormModified, entityType = props.entityType;
    var formDescriptionMaxLength = 2000;
    var hasDescriptionLimitError = description.length > formDescriptionMaxLength;
    var getTextValues = function () {
        switch (entityType) {
            case sharedEnums_1.TicketingEntityType.Ticket:
                return {
                    label: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.event-description' }),
                    placeholder: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.event-description-placeholder' }),
                };
            case sharedEnums_1.TicketingEntityType.Pass:
                return {
                    label: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-description' }),
                    placeholder: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-description-instruction' }),
                };
        }
    };
    var _a = getTextValues(), label = _a.label, placeholder = _a.placeholder;
    return (<uniform_web_1.Textarea label={label} qaId={"description-input".concat(hasDescriptionLimitError ? '-error' : '')} value={description} helpText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.event-description-help-text' }, {
            descriptionCharactersUsed: description.length,
            descriptionMaxLength: formDescriptionMaxLength,
        })} hasError={hasDescriptionLimitError} onChange={function (e) {
            setDescription(e.target.value);
            setFormModified === null || setFormModified === void 0 ? void 0 : setFormModified(true);
        }} placeholder={placeholder}/>);
}
