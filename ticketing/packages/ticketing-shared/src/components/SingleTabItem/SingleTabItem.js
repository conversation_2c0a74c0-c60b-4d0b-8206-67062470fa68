"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleTabItem = SingleTabItem;
var uniform_web_1 = require("@hudl/uniform-web");
var stringUtils_1 = require("../../utility/stringUtils");
var SingleTabItem_module_scss_1 = require("./SingleTabItem.module.scss");
function SingleTabItem(props) {
    var tab = props.tab, setSelectedChildTabReactiveVar = props.setSelectedChildTabReactiveVar;
    return (<div className={SingleTabItem_module_scss_1.default.container}>
      <div className={tab.isSelected ? SingleTabItem_module_scss_1.default.singleTabSelected : SingleTabItem_module_scss_1.default.singleTab} onClick={function () { return setSelectedChildTabReactiveVar(tab.index); }} onKeyDown={function () { return setSelectedChildTabReactiveVar(tab.index); }} role="button" tabIndex={0} data-qa-id={"ticketing-side-menu-tab-".concat((0, stringUtils_1.replaceSpacesWithDashes)(tab.title).toLowerCase())}>
        <uniform_web_1.Text className={SingleTabItem_module_scss_1.default.singleTabTitle}>{tab.title}</uniform_web_1.Text>
      </div>
    </div>);
}
