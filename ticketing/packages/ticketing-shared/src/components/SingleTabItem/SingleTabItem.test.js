"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
require("@testing-library/jest-dom");
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var vitest_1 = require("vitest");
var SingleTabItem_1 = require("./SingleTabItem");
(0, vitest_1.describe)('SingleTabItem', function () {
    var mockSetSelectedChildTabReactiveVar = vitest_1.vi.fn();
    var defaultProps = {
        tab: {
            title: 'Test Tab',
            index: 0,
            isSelected: false,
            dataQaId: 'test-tab',
        },
        setSelectedChildTabReactiveVar: mockSetSelectedChildTabReactiveVar,
    };
    beforeEach(function () {
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.it)('renders tab with correct title', function () {
        (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...defaultProps}/>);
        (0, vitest_1.expect)(react_1.screen.getByText('Test Tab')).toBeInTheDocument();
    });
    (0, vitest_1.it)('applies selected styles when tab is selected', function () {
        var selectedProps = __assign(__assign({}, defaultProps), { tab: __assign(__assign({}, defaultProps.tab), { isSelected: true }) });
        var container = (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...selectedProps}/>).container;
        (0, vitest_1.expect)(container.querySelector('[class*="singleTabSelected"]')).toBeInTheDocument();
    });
    (0, vitest_1.it)('applies unselected styles when tab is not selected', function () {
        var container = (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...defaultProps}/>).container;
        (0, vitest_1.expect)(container.querySelector('[class*="singleTab"]')).toBeInTheDocument();
    });
    (0, vitest_1.it)('calls setSelectedChildTabReactiveVar with correct index on click', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...defaultProps}/>);
                    return [4 /*yield*/, user.click(react_1.screen.getByRole('button'))];
                case 1:
                    _a.sent();
                    (0, vitest_1.expect)(mockSetSelectedChildTabReactiveVar).toHaveBeenCalledWith(0);
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('calls setSelectedChildTabReactiveVar with correct index on pressing Enter', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...defaultProps}/>);
                    return [4 /*yield*/, user.tab()];
                case 1:
                    _a.sent(); // Focus the button
                    return [4 /*yield*/, user.keyboard('{Enter}')];
                case 2:
                    _a.sent();
                    (0, vitest_1.expect)(mockSetSelectedChildTabReactiveVar).toHaveBeenCalledWith(0);
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('sets correct data-qa-id with transformed title', function () {
        var propsWithSpaces = __assign(__assign({}, defaultProps), { tab: __assign(__assign({}, defaultProps.tab), { title: 'Test Tab With Spaces' }) });
        (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...propsWithSpaces}/>);
        (0, vitest_1.expect)(react_1.screen.getByRole('button')).toHaveAttribute('data-qa-id', 'ticketing-side-menu-tab-test-tab-with-spaces');
    });
    (0, vitest_1.it)('has correct accessibility attributes', function () {
        (0, react_1.render)(<SingleTabItem_1.SingleTabItem {...defaultProps}/>);
        var button = react_1.screen.getByRole('button');
        (0, vitest_1.expect)(button).toHaveAttribute('tabIndex', '0');
        (0, vitest_1.expect)(button).toHaveAttribute('role', 'button');
    });
});
