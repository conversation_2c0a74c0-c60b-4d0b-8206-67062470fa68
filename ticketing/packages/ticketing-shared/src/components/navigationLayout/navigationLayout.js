"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NavigationLayout = exports.TabType = void 0;
var react_1 = require("react");
var client_1 = require("@apollo/client");
var mobileNav_1 = require("../mobileNav/mobileNav");
var stateVars_1 = require("../utils/stateVars");
var VerticalNav_1 = require("../verticalNav/VerticalNav/VerticalNav");
var navigationLayout_module_scss_1 = require("./navigationLayout.module.scss");
var TabType;
(function (TabType) {
    TabType["Nested"] = "Nested";
    TabType["Single"] = "Single";
})(TabType || (exports.TabType = TabType = {}));
var NavigationLayout = function (_a) {
    var tabs = _a.tabs, navTitle = _a.navTitle, dataQaId = _a.dataQaId, tabContentMap = _a.tabContentMap, selectedChildHomeTab = _a.selectedChildHomeTab, _b = _a.isLoading, isLoading = _b === void 0 ? false : _b, loadingComponent = _a.loadingComponent;
    var isMobileOrTablet = (0, client_1.useReactiveVar)(stateVars_1.isMediaScreen);
    var selectedChildTab = (0, client_1.useReactiveVar)(selectedChildHomeTab);
    var renderTabContent = function () {
        var tabContent = tabContentMap[selectedChildTab];
        if (!tabContent) {
            throw new Error("Tab content for ".concat(selectedChildTab, " is missing!"));
        }
        return (<div className={navigationLayout_module_scss_1.default.contentWrapper}>
        {tabContent.header}
        <div className={navigationLayout_module_scss_1.default.contentWrapper}>{tabContent.content}</div>
      </div>);
    };
    return (<div className={navigationLayout_module_scss_1.default.tabLayoutContainer} data-qa-id={dataQaId}>
      {isMobileOrTablet ? (<mobileNav_1.default tabs={tabs} dataQaId={dataQaId} setSelectedChildTabReactiveVar={selectedChildHomeTab} navTitle={navTitle} isLoading={isLoading} loadingComponent={loadingComponent}/>) : (<VerticalNav_1.VerticalNav tabs={tabs} dataQaId={dataQaId} setSelectedChildTabReactiveVar={selectedChildHomeTab} isLoading={isLoading} loadingComponent={loadingComponent}/>)}
      <div className={navigationLayout_module_scss_1.default.contentContainer}>{renderTabContent()}</div>
    </div>);
};
exports.NavigationLayout = NavigationLayout;
