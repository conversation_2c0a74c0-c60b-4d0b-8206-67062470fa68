"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Loading = exports.Default = void 0;
var client_1 = require("@apollo/client");
var uniform_web_1 = require("@hudl/uniform-web");
var navigationLayout_1 = require("./navigationLayout");
exports.default = {
    title: 'Navigation/NavigationLayout',
    component: navigationLayout_1.NavigationLayout,
    parameters: {
        layout: 'fullscreen',
        backgrounds: {
            default: 'light',
        },
    },
};
// Mock data for tabs
var mockSelectedChildHomeTab = (0, client_1.makeVar)(0);
var mockSingleTabs = [
    {
        type: navigationLayout_1.TabType.Single,
        tab: {
            title: 'Dashboard',
            index: 0,
            isSelected: true,
        },
    },
    {
        type: navigationLayout_1.TabType.Single,
        tab: {
            title: 'Reports',
            index: 1,
            isSelected: false,
        },
    },
];
var mockNestedTabs = [
    {
        type: navigationLayout_1.TabType.Nested,
        tab: {
            parentTitle: 'Event Settings',
            isOpenByDefault: true,
            children: [
                {
                    title: 'General',
                    index: 2,
                    isSelected: false,
                },
                {
                    title: 'Pricing',
                    index: 3,
                    isSelected: false,
                },
                {
                    title: 'Tickets',
                    index: 4,
                    isSelected: false,
                },
            ],
        },
    },
];
var mixedTabs = __spreadArray(__spreadArray([], mockSingleTabs, true), mockNestedTabs, true);
// Create mock content for each tab
var mockTabContentMap = {
    0: {
        header: <uniform_web_1.Headline level="1">Dashboard</uniform_web_1.Headline>,
        content: <div style={{ padding: '20px' }}>Dashboard content goes here</div>,
    },
    1: {
        header: <uniform_web_1.Headline level="1">Reports</uniform_web_1.Headline>,
        content: <div style={{ padding: '20px' }}>Reports content goes here</div>,
    },
    2: {
        header: <uniform_web_1.Headline level="1">General Settings</uniform_web_1.Headline>,
        content: <div style={{ padding: '20px' }}>General settings content goes here</div>,
    },
    3: {
        header: <uniform_web_1.Headline level="1">Pricing Settings</uniform_web_1.Headline>,
        content: <div style={{ padding: '20px' }}>Pricing settings content goes here</div>,
    },
    4: {
        header: <uniform_web_1.Headline level="1">Tickets Settings</uniform_web_1.Headline>,
        content: <div style={{ padding: '20px' }}>Ticket settings content goes here</div>,
    },
};
exports.Default = {
    args: {
        tabs: mixedTabs,
        navTitle: 'Ticketing',
        dataQaId: 'ticketing-nav',
        tabContentMap: mockTabContentMap,
        selectedChildHomeTab: mockSelectedChildHomeTab,
        isLoading: false,
    },
};
exports.Loading = {
    args: {
        tabs: mixedTabs,
        navTitle: 'Ticketing',
        dataQaId: 'ticketing-nav-loading',
        tabContentMap: mockTabContentMap,
        selectedChildHomeTab: mockSelectedChildHomeTab,
        isLoading: true,
    },
};
