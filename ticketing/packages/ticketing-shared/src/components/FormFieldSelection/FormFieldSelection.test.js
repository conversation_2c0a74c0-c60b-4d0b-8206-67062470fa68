"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var ticketingMockData_1 = require("../../mockData/ticketingMockData");
var renderHelpers_1 = require("../../test/renderHelpers");
var FormFieldSelection_1 = require("./FormFieldSelection");
var organizationId = 'organizationId';
vi.mock('react-router-dom', function () { return ({
    useParams: vi.fn(function () {
        return { organizationId: organizationId };
    }),
}); });
var formFieldOne = (0, ticketingMockData_1.aFormField)({ id: '1', label: 'Field One', helpText: 'Help Field One', isRequired: true });
var formFieldTwo = (0, ticketingMockData_1.aFormField)({ id: '2', label: 'Field Two', helpText: 'Help Field Two', isRequired: false });
var formFieldThree = (0, ticketingMockData_1.aFormField)({ id: '3', label: 'Field Three', helpText: 'Help Field Three', isRequired: true });
var formFieldFour = (0, ticketingMockData_1.aFormField)({ id: '4', label: 'Field Four', helpText: 'Help Field Four', isRequired: false });
var formFieldFive = (0, ticketingMockData_1.aFormField)({ id: '5', label: 'Field Five', helpText: 'Help Field Five', isRequired: true });
var formFieldSix = (0, ticketingMockData_1.aFormField)({ id: '6', label: 'Field Six', helpText: 'Help Field Six', isRequired: false });
var formFieldSeven = (0, ticketingMockData_1.aFormField)({ id: '7', label: 'Field Seven', helpText: 'Help Field Seven', isRequired: true });
var formFieldEight = (0, ticketingMockData_1.aFormField)({ id: '8', label: 'Field Eight', helpText: 'Help Field Eight', isRequired: false });
var formFieldNine = (0, ticketingMockData_1.aFormField)({ id: '9', label: 'Field Nine', helpText: 'Help Field Nine', isRequired: true });
var formFieldTen = (0, ticketingMockData_1.aFormField)({ id: '10', label: 'Field Ten', helpText: 'Help Field Ten', isRequired: false });
var allResults = [
    formFieldOne,
    formFieldTwo,
    formFieldThree,
    formFieldFour,
    formFieldFive,
    formFieldSix,
    formFieldSeven,
    formFieldEight,
    formFieldNine,
    formFieldTen,
];
describe('FormFieldSelection -- UI', function () {
    it('Renders with no form fields selected', function () {
        (0, renderHelpers_1.renderWithOptions)(<FormFieldSelection_1.FormFieldSelection createFormField={vi.fn()} setIsTicketedEventModified={vi.fn()} selectedFormFields={[]} setSelectedFormFields={vi.fn()} formFields={allResults} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('form-field-selection-container')).toBeInTheDocument();
        expect(react_1.screen.queryByTestId('form-field-selection-form-field-item-1')).toBeFalsy();
        expect(react_1.screen.getByTestId('form-field-selection-add-field-button')).toBeEnabled();
        expect(react_1.screen.getByTestId('form-field-selection-select')).toBeEnabled();
        expect(react_1.screen.queryByTestId('form-field-selection-limit-hit-error-message')).toBeFalsy();
    });
    it('Renders with form fields selected', function () {
        (0, renderHelpers_1.renderWithOptions)(<FormFieldSelection_1.FormFieldSelection createFormField={vi.fn()} setIsTicketedEventModified={vi.fn()} selectedFormFields={[formFieldOne, formFieldTwo]} setSelectedFormFields={vi.fn()} formFields={allResults} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('form-field-selection-container')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1-label')).toHaveTextContent('Field One');
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1-required')).toHaveTextContent('Required: Yes');
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2-label')).toHaveTextContent('Field Two');
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2-help-text')).toHaveTextContent('Help Field Two');
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2-required')).toHaveTextContent('Required: No');
        expect(react_1.screen.getByTestId('form-field-selection-add-field-button')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-select')).toBeEnabled();
    });
});
describe('FormFieldSelection -- Interaction', function () {
    it('Selecting Existing Field Adds To List', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, setSelectedFormFields, formFieldSelect;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    setSelectedFormFields = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<FormFieldSelection_1.FormFieldSelection createFormField={vi.fn()} setIsTicketedEventModified={vi.fn()} selectedFormFields={[formFieldOne, formFieldTwo]} setSelectedFormFields={setSelectedFormFields} formFields={allResults} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('form-field-selection-container')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-select')).toBeEnabled();
                    formFieldSelect = react_1.screen.getByTestId('form-field-selection-select-select');
                    expect(formFieldSelect).toBeEnabled();
                    return [4 /*yield*/, user.click(formFieldSelect)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, react_1.screen.findByTestId("form-field-selection-select-3-option")];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId("form-field-selection-select-3-option"))];
                case 3:
                    _a.sent();
                    expect(setSelectedFormFields).toHaveBeenCalledWith([formFieldOne, formFieldTwo, formFieldThree]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Removing Field From List Removes', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, setSelectedFormFields;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    setSelectedFormFields = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<FormFieldSelection_1.FormFieldSelection createFormField={vi.fn()} setIsTicketedEventModified={vi.fn()} selectedFormFields={[formFieldOne, formFieldTwo, formFieldThree]} setSelectedFormFields={setSelectedFormFields} formFields={allResults} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('form-field-selection-container')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('form-field-selection-form-field-item-3')).toBeInTheDocument();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('form-field-selection-form-field-item-3-remove-button'))];
                case 1:
                    _a.sent();
                    expect(setSelectedFormFields).toHaveBeenCalledWith([formFieldOne, formFieldTwo]);
                    return [2 /*return*/];
            }
        });
    }); });
});
describe('FormFieldSelection -- Limits/Errors', function () {
    it('Ten Selected, No More Can Be Added', function () {
        var setSelectedFormFields = vi.fn();
        (0, renderHelpers_1.renderWithOptions)(<FormFieldSelection_1.FormFieldSelection createFormField={vi.fn()} setIsTicketedEventModified={vi.fn()} selectedFormFields={allResults} setSelectedFormFields={setSelectedFormFields} formFields={allResults} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('form-field-selection-container')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-1')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-2')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-3')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-4')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-5')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-6')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-7')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-8')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-9')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-form-field-item-10')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-add-field-button-disabled')).toBeDisabled();
        expect(react_1.screen.getByTestId('form-field-selection-select-disabled')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('form-field-selection-limit-hit-error-message')).toBeInTheDocument();
    });
});
