"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormFieldSelection = FormFieldSelection;
var react_1 = require("react");
var react_router_dom_1 = require("react-router-dom");
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_button_legacy_1 = require("@hudl/uniform-web-button-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var AddFormFieldModal_1 = require("../AddFormFieldModal/AddFormFieldModal");
var NoOptionsMessage_1 = require("../selectComponents/NoOptionsMessage/NoOptionsMessage");
var FormFieldSelection_module_scss_1 = require("./FormFieldSelection.module.scss");
function FormFieldSelection(props) {
    var createFormField = props.createFormField, setIsTicketedEventModified = props.setIsTicketedEventModified, setSelectedFormFields = props.setSelectedFormFields, selectedFormFields = props.selectedFormFields, formFields = props.formFields, showCreateFormFieldErrorToast = props.showCreateFormFieldErrorToast, setShowCreateFormFieldErrorToast = props.setShowCreateFormFieldErrorToast;
    var organizationId = (0, react_router_dom_1.useParams)().organizationId;
    var _a = (0, react_1.useState)(false), addFormFieldModalOpen = _a[0], setAddFormFieldModalOpen = _a[1];
    var selectedFormFieldLimit = 10;
    var formFieldLimitHit = selectedFormFields.length >= selectedFormFieldLimit;
    var onFieldSelect = function (option) {
        setIsTicketedEventModified === null || setIsTicketedEventModified === void 0 ? void 0 : setIsTicketedEventModified(true);
        var newSelectedFormFields = __spreadArray([], selectedFormFields, true);
        var selectedFormField = formFields.find(function (f) { return f.id === option.value; });
        selectedFormField && newSelectedFormFields.push(selectedFormField);
        setSelectedFormFields(newSelectedFormFields);
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    };
    var onFieldRemoved = function (formField) {
        setIsTicketedEventModified === null || setIsTicketedEventModified === void 0 ? void 0 : setIsTicketedEventModified(true);
        var newSelectedFormFields = selectedFormFields.filter(function (s) { return s.id !== formField.id; });
        setSelectedFormFields(newSelectedFormFields);
    };
    var getNoOptionsMessage = function () {
        return (<NoOptionsMessage_1.NoOptionsMessage message={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.select.empty-message' })} qaId="form-field-selection-no-options-message"/>);
    };
    var renderExistingFieldSelector = function () {
        var _a;
        var unselectedFormFields = formFields === null || formFields === void 0 ? void 0 : formFields.filter(function (formField) { return !selectedFormFields.some(function (s) { return s.id === formField.id; }); }).sort(function (a, b) { return a.label.localeCompare(b.label); });
        var selectOptions = (_a = unselectedFormFields === null || unselectedFormFields === void 0 ? void 0 : unselectedFormFields.map(function (formField) { return ({
            label: formField.label,
            value: formField.id,
        }); })) !== null && _a !== void 0 ? _a : [];
        return (<div className={FormFieldSelection_module_scss_1.default.formFieldSelectContainer}>
        <uniform_web_1.Select label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.select.label' })} value={null} qaId={"form-field-selection-select".concat(formFieldLimitHit ? '-disabled' : '')} onChange={function (newValue) { return onFieldSelect(newValue); }} options={selectOptions} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.select.placeholder' })} components={{ NoOptionsMessage: getNoOptionsMessage }} isDisabled={formFieldLimitHit} closeMenuOnSelect={false}/>
      </div>);
    };
    var renderAddFieldButton = function () {
        return (<div className={FormFieldSelection_module_scss_1.default.addFormFieldButtonContainer}>
        <uniform_web_button_legacy_1.Button size="small" buttonType="secondary" qaId={"form-field-selection-add-field-button".concat(formFieldLimitHit ? '-disabled' : '')} onClick={function () { return setAddFormFieldModalOpen(true); }} isDisabled={formFieldLimitHit}>
          <uniform_web_1.IconAdd />
          {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-form-field' })}
        </uniform_web_button_legacy_1.Button>
        {formFieldLimitHit && (<div className={FormFieldSelection_module_scss_1.default.limitHitContainer}>
            <uniform_web_1.IconInformation color="contrast" size="small"/>
            <uniform_web_1.Text color="contrast" qaId="form-field-selection-limit-hit-error-message">
              {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-form-field.max-form-fields-error-message' }, { maxFormFields: selectedFormFieldLimit })}
            </uniform_web_1.Text>
          </div>)}
      </div>);
    };
    var renderFormFieldItem = function (formField) {
        var _a;
        var isRequiredYesNo = formField.isRequired
            ? (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.item.required.yes' })
            : (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.item.required.no' });
        var requiredText = "".concat((0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.item.required' }), ": ").concat(isRequiredYesNo);
        return (<div className={FormFieldSelection_module_scss_1.default.formFieldItemContainer} data-qa-id={"form-field-selection-form-field-item-".concat(formField.id)}>
        <uniform_web_1.Subhead className={FormFieldSelection_module_scss_1.default.formFieldItemLabel} qaId={"form-field-selection-form-field-item-".concat(formField.id, "-label")}>
          {formField.label}
        </uniform_web_1.Subhead>
        <div className={FormFieldSelection_module_scss_1.default.formFieldItemHelpTextContainer}>
          {((_a = formField.helpText) === null || _a === void 0 ? void 0 : _a.length) && (<uniform_web_1.Text className={FormFieldSelection_module_scss_1.default.formFieldItemHelpText} qaId={"form-field-selection-form-field-item-".concat(formField.id, "-help-text")}>
              {"".concat((0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.item.help-text' }), ": ").concat(formField.helpText)}
            </uniform_web_1.Text>)}
        </div>
        <uniform_web_1.Text className={FormFieldSelection_module_scss_1.default.formFieldItemRequiredText} qaId={"form-field-selection-form-field-item-".concat(formField.id, "-required")}>
          {requiredText}
        </uniform_web_1.Text>
        <div>
          <uniform_web_button_legacy_1.Button className={FormFieldSelection_module_scss_1.default.formFieldItemButton} size="small" buttonStyle="minimal" qaId={"form-field-selection-form-field-item-".concat(formField.id, "-remove-button")} buttonType="subtle" onClick={function () { return onFieldRemoved(formField); }} icon={<uniform_web_1.IconDelete />}/>
        </div>
      </div>);
    };
    var renderSelectedFormFieldItems = function () {
        return (<div className={FormFieldSelection_module_scss_1.default.selectedFormFieldsContainer}>
        {selectedFormFields.map(function (s) {
                var formField = formFields.find(function (f) { return f.id === s.id; });
                return formField ? renderFormFieldItem(formField) : null;
            })}
      </div>);
    };
    var renderContent = function () {
        return (<div>
        {selectedFormFields.length > 0 && renderSelectedFormFieldItems()}
        {renderExistingFieldSelector()}
        {renderAddFieldButton()}
      </div>);
    };
    return (<div>
      <div data-qa-id="form-field-selection-container">
        <div className={FormFieldSelection_module_scss_1.default.textContainer}>
          <uniform_web_1.Text className={FormFieldSelection_module_scss_1.default.headerText}>
            {(0, frontends_i18n_1.formatMessage)({
            id: 'ticketing-shared.form-fields.additional-fan-information.header',
        })}
          </uniform_web_1.Text>
          <uniform_web_1.Text>
            {(0, frontends_i18n_1.formatMessage)({
            id: 'ticketing-shared.form-fields.additional-fan-information.subhead',
        })}
          </uniform_web_1.Text>
        </div>
        {renderContent()}
      </div>
      <AddFormFieldModal_1.default isOpen={addFormFieldModalOpen} closeModal={function () { return setAddFormFieldModalOpen(false); }} organizationId={organizationId} createFormField={createFormField} showCreateFormFieldErrorToast={showCreateFormFieldErrorToast} setShowCreateFormFieldErrorToast={setShowCreateFormFieldErrorToast}/>
    </div>);
}
