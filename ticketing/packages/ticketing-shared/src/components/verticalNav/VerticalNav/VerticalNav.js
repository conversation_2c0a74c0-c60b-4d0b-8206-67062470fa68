"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerticalNav = VerticalNav;
var navigationLayout_1 = require("../../navigationLayout/navigationLayout");
var SingleTabItem_1 = require("../../SingleTabItem/SingleTabItem");
var NavGrouping_1 = require("../NavGrouping/NavGrouping");
var VerticalNav_module_scss_1 = require("./VerticalNav.module.scss");
function VerticalNav(_a) {
    var tabs = _a.tabs, dataQaId = _a.dataQaId, setSelectedChildTabReactiveVar = _a.setSelectedChildTabReactiveVar, isLoading = _a.isLoading, loadingComponent = _a.loadingComponent;
    var renderTab = function (tabItem, index) {
        if (tabItem.type === navigationLayout_1.TabType.Nested) {
            var tab = tabItem.tab;
            return (<NavGrouping_1.NavGrouping key={"nested-".concat(index)} parentTitle={tab.parentTitle} childTabs={tab.children} isOpenByDefault={tab.isOpenByDefault} allowCollapse={tab.allowCollapse} setSelectedChildTabReactiveVar={setSelectedChildTabReactiveVar}/>);
        }
        else if (tabItem.type === navigationLayout_1.TabType.Single) {
            var tab = tabItem.tab;
            return (<SingleTabItem_1.SingleTabItem key={"single-".concat(index)} tab={tab} setSelectedChildTabReactiveVar={setSelectedChildTabReactiveVar}/>);
        }
    };
    return (<div className={VerticalNav_module_scss_1.default.verticalNavContainer} data-qa-id={"".concat(dataQaId, "-side-menu")}>
      <div className={VerticalNav_module_scss_1.default.tabsContainer}>
        {isLoading && loadingComponent ? loadingComponent : tabs.map(renderTab)}
      </div>
    </div>);
}
