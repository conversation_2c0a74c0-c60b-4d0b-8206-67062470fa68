"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MixedTabTypes = exports.WithNestedTabs = exports.WithSingleTabs = void 0;
var client_1 = require("@apollo/client");
var navigationLayout_1 = require("../../navigationLayout/navigationLayout");
var VerticalNav_1 = require("./VerticalNav");
exports.default = {
    title: 'Navigation/VerticalNav',
    component: VerticalNav_1.VerticalNav,
    parameters: {
        backgrounds: {
            default: 'dark',
        },
    },
};
// Mock data
var mockSelectedChildTabReactiveVar = (0, client_1.makeVar)(0);
var mockSingleTabs = [
    {
        type: navigationLayout_1.TabType.Single,
        tab: {
            title: 'Dashboard',
            index: 0,
            isSelected: true,
        },
    },
    {
        type: navigationLayout_1.TabType.Single,
        tab: {
            title: 'Reports',
            index: 1,
            isSelected: false,
        },
    },
];
var mockNestedTabs = [
    {
        type: navigationLayout_1.TabType.Nested,
        tab: {
            parentTitle: 'Event Settings',
            isOpenByDefault: true,
            children: [
                {
                    title: 'General',
                    index: 0,
                    isSelected: true,
                },
                {
                    title: 'Pricing',
                    index: 1,
                    isSelected: false,
                },
                {
                    title: 'Tickets',
                    index: 2,
                    isSelected: false,
                },
            ],
        },
    },
    {
        type: navigationLayout_1.TabType.Nested,
        tab: {
            parentTitle: 'User Management',
            isOpenByDefault: false,
            children: [
                {
                    title: 'Roles',
                    index: 3,
                    isSelected: false,
                },
                {
                    title: 'Permissions',
                    index: 4,
                    isSelected: false,
                },
            ],
        },
    },
];
var mixedTabs = __spreadArray(__spreadArray([], mockSingleTabs, true), mockNestedTabs, true);
exports.WithSingleTabs = {
    args: {
        tabs: mockSingleTabs,
        dataQaId: 'single-tabs-example',
        setSelectedChildTabReactiveVar: mockSelectedChildTabReactiveVar,
        isLoading: false,
    },
};
exports.WithNestedTabs = {
    args: {
        tabs: mockNestedTabs,
        dataQaId: 'nested-tabs-example',
        setSelectedChildTabReactiveVar: mockSelectedChildTabReactiveVar,
        isLoading: false,
    },
};
exports.MixedTabTypes = {
    args: {
        tabs: mixedTabs,
        dataQaId: 'mixed-tabs-example',
        setSelectedChildTabReactiveVar: mockSelectedChildTabReactiveVar,
        isLoading: false,
    },
};
