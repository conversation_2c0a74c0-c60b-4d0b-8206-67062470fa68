"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NavItem = NavItem;
var uniform_web_1 = require("@hudl/uniform-web");
var NavItem_module_scss_1 = require("./NavItem.module.scss");
function NavItem(_a) {
    var title = _a.title, parent = _a.parent, index = _a.index, isSelected = _a.isSelected, _b = _a.onSelected, onSelected = _b === void 0 ? function () { } : _b;
    return (<button value={index} className={isSelected ? NavItem_module_scss_1.default.tabButtonSelected : NavItem_module_scss_1.default.tabButton} type="button" onClick={onSelected} data-qa-id={"".concat(parent, "-").concat(title.split(' ').join('-').toLowerCase(), "-menu-item").concat(isSelected ? '-selected' : '')}>
      <uniform_web_1.Text className={isSelected ? NavItem_module_scss_1.default.tabTitleSelected : NavItem_module_scss_1.default.tabTitle}>{title} </uniform_web_1.Text>
    </button>);
}
exports.default = NavItem;
