"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
require("@testing-library/jest-dom");
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var vitest_1 = require("vitest");
var NavGrouping_1 = require("./NavGrouping");
(0, vitest_1.describe)('NavGrouping', function () {
    var mockSetSelectedChildTabReactiveVar = vitest_1.vi.fn();
    var defaultProps = {
        parentTitle: 'Test Group',
        isOpenByDefault: false,
        childTabs: [
            { title: 'Child 1', index: 0, isSelected: false, dataQaId: 'child-1' },
            { title: 'Child 2', index: 1, isSelected: true, dataQaId: 'child-2' },
            { title: 'Child 3', index: 2, isSelected: false, dataQaId: 'child-3' },
        ],
        setSelectedChildTabReactiveVar: mockSetSelectedChildTabReactiveVar,
    };
    beforeEach(function () {
        vitest_1.vi.clearAllMocks();
    });
    (0, vitest_1.it)('renders with correct parent title', function () {
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
        (0, vitest_1.expect)(react_1.screen.getByText('Test Group')).toBeInTheDocument();
    });
    (0, vitest_1.it)('starts collapsed when isOpenByDefault is false', function () {
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
        (0, vitest_1.expect)(react_1.screen.queryByText('Child 1')).not.toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.queryByText('Child 2')).not.toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.queryByText('Child 3')).not.toBeInTheDocument();
    });
    (0, vitest_1.it)('starts expanded when isOpenByDefault is true', function () {
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps} isOpenByDefault={true}/>);
        (0, vitest_1.expect)(react_1.screen.getByText('Child 1')).toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.getByText('Child 2')).toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.getByText('Child 3')).toBeInTheDocument();
    });
    (0, vitest_1.it)('toggles expansion when clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
                    // Initially collapsed
                    (0, vitest_1.expect)(react_1.screen.queryByText('Child 1')).not.toBeInTheDocument();
                    // Click to expand
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('test-group-menu-category-collapsed'))];
                case 1:
                    // Click to expand
                    _a.sent();
                    (0, vitest_1.expect)(react_1.screen.getByText('Child 1')).toBeInTheDocument();
                    // Click to collapse
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('test-group-menu-category-expanded'))];
                case 2:
                    // Click to collapse
                    _a.sent();
                    (0, vitest_1.expect)(react_1.screen.queryByText('Child 1')).not.toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('toggles expansion when pressing Enter', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
                    // Initially collapsed
                    (0, vitest_1.expect)(react_1.screen.queryByText('Child 1')).not.toBeInTheDocument();
                    // Press Enter to expand
                    return [4 /*yield*/, user.tab()];
                case 1:
                    // Press Enter to expand
                    _a.sent();
                    return [4 /*yield*/, user.keyboard('{Enter}')];
                case 2:
                    _a.sent();
                    (0, vitest_1.expect)(react_1.screen.getByText('Child 1')).toBeInTheDocument();
                    // Press Enter to collapse
                    return [4 /*yield*/, user.keyboard('{Enter}')];
                case 3:
                    // Press Enter to collapse
                    _a.sent();
                    (0, vitest_1.expect)(react_1.screen.queryByText('Child 1')).not.toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('calls setSelectedChildTabReactiveVar when child tab is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps} isOpenByDefault={true}/>);
                    return [4 /*yield*/, user.click(react_1.screen.getByText('Child 1'))];
                case 1:
                    _a.sent();
                    (0, vitest_1.expect)(mockSetSelectedChildTabReactiveVar).toHaveBeenCalledWith(0);
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('has correct accessibility attributes', function () {
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
        var button = react_1.screen.getByRole('button');
        (0, vitest_1.expect)(button).toHaveAttribute('tabIndex', '0');
    });
    (0, vitest_1.it)('sets correct data-qa-ids', function () {
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
        // Parent group qa-id
        (0, vitest_1.expect)(react_1.screen.getByRole('button')).toHaveAttribute('data-qa-id', 'test-group-menu-category-collapsed');
        // Parent title qa-id
        (0, vitest_1.expect)(react_1.screen.getByText('Test Group')).toHaveAttribute('data-qa-id', 'test-group-title');
    });
    (0, vitest_1.it)('updates data-qa-id when toggled', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, button;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
                    button = react_1.screen.getByRole('button');
                    (0, vitest_1.expect)(button).toHaveAttribute('data-qa-id', 'test-group-menu-category-collapsed');
                    return [4 /*yield*/, user.click(button)];
                case 1:
                    _a.sent();
                    (0, vitest_1.expect)(button).toHaveAttribute('data-qa-id', 'test-group-menu-category-expanded');
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('renders correct icon based on expanded state', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    (0, react_1.render)(<NavGrouping_1.NavGrouping {...defaultProps}/>);
                    // Initially shows expand icon
                    (0, vitest_1.expect)(react_1.screen.getByLabelText('Expand Collapse Down')).toBeInTheDocument();
                    (0, vitest_1.expect)(react_1.screen.queryByLabelText('Expand Collapse Up')).not.toBeInTheDocument();
                    // After expanding shows collapse icon
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('test-group-menu-category-collapsed'))];
                case 1:
                    // After expanding shows collapse icon
                    _a.sent();
                    (0, vitest_1.expect)(react_1.screen.queryByLabelText('Expand Collapse Down')).not.toBeInTheDocument();
                    (0, vitest_1.expect)(react_1.screen.getByLabelText('Expand Collapse Up')).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    (0, vitest_1.it)('icon does not render when allowCollapsed = false', function () {
        var props = __assign(__assign({}, defaultProps), { allowCollapse: false });
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...props}/>);
        (0, vitest_1.expect)(react_1.screen.queryByLabelText('Expand Collapse Down')).not.toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.queryByLabelText('Expand Collapse Up')).not.toBeInTheDocument();
    });
    (0, vitest_1.it)('renders and shows child tabs open by default when allowCollapse is false', function () {
        var props = __assign(__assign({}, defaultProps), { allowCollapse: false });
        (0, react_1.render)(<NavGrouping_1.NavGrouping {...props}/>);
        (0, vitest_1.expect)(react_1.screen.getByText('Child 1')).toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.getByText('Child 2')).toBeInTheDocument();
        (0, vitest_1.expect)(react_1.screen.getByText('Child 3')).toBeInTheDocument();
    });
});
