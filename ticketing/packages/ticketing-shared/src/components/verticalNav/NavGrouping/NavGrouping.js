"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NavGrouping = NavGrouping;
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var NavItem_1 = require("../NavItem/NavItem");
var NavGrouping_module_scss_1 = require("./NavGrouping.module.scss");
function NavGrouping(props) {
    var parentTitle = props.parentTitle, childTabs = props.childTabs, setSelectedChildTabReactiveVar = props.setSelectedChildTabReactiveVar, isOpenByDefault = props.isOpenByDefault, _a = props.allowCollapse, allowCollapse = _a === void 0 ? true : _a;
    var _b = (0, react_1.useState)(!allowCollapse ? true : isOpenByDefault), isOpen = _b[0], setIsOpen = _b[1]; // Default to open if not collapsible
    var qaIdTitle = parentTitle.split(' ').join('-').toLowerCase();
    var parentOnClick = (0, react_1.useCallback)(function () {
        if (allowCollapse) {
            setIsOpen(!isOpen);
        }
    }, [isOpen, allowCollapse]);
    return (<div className={NavGrouping_module_scss_1.default.navGroupingContainer}>
      <div className={NavGrouping_module_scss_1.default.parentTab} onClick={parentOnClick} onKeyDown={parentOnClick} role="button" tabIndex={0} data-qa-id={"".concat(qaIdTitle, "-menu-category-").concat(isOpen ? 'expanded' : 'collapsed')}>
        <uniform_web_1.Text className={NavGrouping_module_scss_1.default.parentTabTitle} qaId={"".concat(qaIdTitle, "-title")}>
          {parentTitle}
        </uniform_web_1.Text>
        {allowCollapse ? (isOpen ? (<uniform_web_1.IconUiExpandCollapseUp size="small"/>) : (<uniform_web_1.IconUiExpandCollapseDown size="small"/>)) : null}
      </div>
      {isOpen &&
            childTabs.map(function (child) { return (<NavItem_1.default key={child.index} title={child.title} parent={parentTitle.toLowerCase()} index={child.index} isSelected={child.isSelected} onSelected={function () {
                    setSelectedChildTabReactiveVar(child.index);
                }}/>); })}
    </div>);
}
exports.default = NavGrouping;
