"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassConfigFormQuantityInput = PassConfigFormQuantityInput;
var react_1 = require("react");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var constants_1 = require("../../types/constants");
var PassConfigFormQuantityInput_module_scss_1 = require("./PassConfigFormQuantityInput.module.scss");
function PassConfigFormQuantityInput(props) {
    var isEdit = props.isEdit, initialQuantity = props.initialQuantity, setFormModified = props.setFormModified, setPassConfigQuantityAvailable = props.setPassConfigQuantityAvailable, setPassConfigQuantityAvailableError = props.setPassConfigQuantityAvailableError, passConfigQuantityAvailable = props.passConfigQuantityAvailable, passConfigQuantityAvailableError = props.passConfigQuantityAvailableError;
    var _a = (0, react_1.useState)(false), quantityLessThanExisting = _a[0], setQuantityLessThanExisting = _a[1];
    var onQuantityAvailableChange = function (e) {
        if (e.target.value.length > constants_1.quantityInputMaxLength || e.target.value.includes('.')) {
            return;
        }
        setPassConfigQuantityAvailable(e.target.value);
        var quantityValue = parseInt(e.target.value, 10);
        var quantityValueLessThanExisting = false;
        if (isEdit) {
            quantityValueLessThanExisting =
                initialQuantity || initialQuantity === 0 ? quantityValue < initialQuantity : !Number.isNaN(quantityValue);
            setQuantityLessThanExisting(quantityValueLessThanExisting);
        }
        setPassConfigQuantityAvailableError((!isEdit && quantityValue === 0) || (!!quantityValue && (Number.isNaN(quantityValue) || quantityValue <= 0)));
        setFormModified === null || setFormModified === void 0 ? void 0 : setFormModified(true);
    };
    var getQuantityHelpText = function () {
        if (passConfigQuantityAvailableError) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-quantity-error' });
        }
        if (quantityLessThanExisting) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.quantity-less-than-existing' });
        }
        return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.quantity-help-text' });
    };
    return (<div className={PassConfigFormQuantityInput_module_scss_1.default.passQuantityInputContainer}>
      <uniform_web_forms_legacy_1.Input type="number" label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-quantity-label' })} value={passConfigQuantityAvailable} hasError={passConfigQuantityAvailableError} helpText={getQuantityHelpText()} onChange={onQuantityAvailableChange} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-quantity-placeholder' })} qaId={"pass-quantity-available-input".concat(passConfigQuantityAvailableError ? '-error' : '')}/>
    </div>);
}
