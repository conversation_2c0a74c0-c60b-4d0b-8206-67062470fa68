"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var frontends_i18n_1 = require("frontends-i18n");
var sharedEnums_1 = require("../../enums/sharedEnums");
var PriceInput_1 = require("../PriceInput/PriceInput");
var TicketTypePriceInput_module_scss_1 = require("./TicketTypePriceInput.module.scss");
function TicketTypePriceInput(props) {
    var price = props.price, ticketTypeNotSet = props.ticketTypeNotSet, onChange = props.onChange, hasPriceError = props.hasPriceError, freeTicketingOnly = props.freeTicketingOnly;
    var _a = (0, react_1.useState)(price !== null && price !== void 0 ? price : 0), displayPrice = _a[0], setDisplayPrice = _a[1];
    var onPriceChange = function (newPrice) {
        if (newPrice === displayPrice) {
            return;
        }
        setDisplayPrice(newPrice);
        onChange(newPrice);
    };
    var helpText = (0, react_1.useMemo)(function () {
        if (ticketTypeNotSet && !freeTicketingOnly) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.ticket-pricing-help-text' });
        }
    }, [freeTicketingOnly, ticketTypeNotSet]);
    return (<div className={TicketTypePriceInput_module_scss_1.default.wrapper}>
      <div className={TicketTypePriceInput_module_scss_1.default.flexInput}>
        <PriceInput_1.PriceInput currency={sharedEnums_1.Currency.USD} priceInCents={displayPrice} onPriceChange={onPriceChange} isReadOnly={freeTicketingOnly || ticketTypeNotSet} hasError={hasPriceError} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.price' })} errorMessage={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.fee-strategy.ticket-price-minimum' })} helpText={helpText}/>
      </div>
    </div>);
}
exports.default = TicketTypePriceInput;
