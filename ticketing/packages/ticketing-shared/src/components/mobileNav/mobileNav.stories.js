"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Demo = void 0;
var navigationLayout_1 = require("../navigationLayout/navigationLayout");
var mobileNav_1 = require("./mobileNav");
exports.default = {
    title: 'MobileNav',
    component: mobileNav_1.default,
};
exports.Demo = {
    render: function (args) {
        return <mobileNav_1.default {...args}/>;
    },
    argTypes: {
        tabs: {
            control: {
                type: 'object',
            },
        },
    },
    args: {
        tabs: [
            {
                type: navigationLayout_1.TabType.Single,
                tab: {
                    title: 'Single Tab',
                    index: 0,
                    dataQaId: 'single-tab',
                    isSelected: false,
                },
            },
            {
                type: navigationLayout_1.TabType.Nested,
                tab: {
                    parentTitle: 'Nested Tab',
                    isOpenByDefault: true,
                    children: [
                        {
                            title: 'Nested Tab 1',
                            index: 0,
                            dataQaId: 'nested-tab-1',
                            isSelected: true,
                        },
                        {
                            title: 'Nested Tab 2',
                            index: 1,
                            dataQaId: 'nested-tab-2',
                            isSelected: false,
                        },
                    ],
                },
            },
        ],
        setSelectedChildTabReactiveVar: function (index) { return console.log('Selected tab:', index); },
        navTitle: 'Example Menu',
        dataQaId: 'mobile-nav-demo',
    },
};
