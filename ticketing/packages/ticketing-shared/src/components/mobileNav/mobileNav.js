"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = MobileNav;
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var VerticalNav_1 = require("../verticalNav/VerticalNav/VerticalNav");
var mobileNav_module_scss_1 = require("./mobileNav.module.scss");
function MobileNav(_a) {
    var tabs = _a.tabs, setSelectedChildTabReactiveVar = _a.setSelectedChildTabReactiveVar, navTitle = _a.navTitle, dataQaId = _a.dataQaId, isLoading = _a.isLoading, loadingComponent = _a.loadingComponent;
    var _b = (0, react_1.useState)(false), isNavOpen = _b[0], setIsNavOpen = _b[1];
    var handleTabSelect = function (index) {
        setSelectedChildTabReactiveVar(index);
        setIsNavOpen(false);
    };
    return (<div className={mobileNav_module_scss_1.default.mobileNavContainer}>
      <div className={mobileNav_module_scss_1.default.mobileNavHeader} onClick={function () { return setIsNavOpen(!isNavOpen); }} onKeyDown={function () { return setIsNavOpen(!isNavOpen); }} role="button" tabIndex={0}>
        <uniform_web_1.ItemTitle className={mobileNav_module_scss_1.default.mobileNavMenuTitle}>{navTitle}</uniform_web_1.ItemTitle>
        {isNavOpen ? <uniform_web_1.IconUiExpandCollapseUp size="small"/> : <uniform_web_1.IconUiExpandCollapseDown size="small"/>}
      </div>
      <uniform_web_1.Overlay isOpen={isNavOpen} onClose={function () { return setIsNavOpen(!isNavOpen); }} className={mobileNav_module_scss_1.default['u-overlay']}>
        <div className={mobileNav_module_scss_1.default.mobileNavWrapper}>
          <uniform_web_1.Headline className={mobileNav_module_scss_1.default.mobileNavHeadline} level="2">
            {navTitle}
          </uniform_web_1.Headline>
          <VerticalNav_1.VerticalNav tabs={tabs} dataQaId={"".concat(dataQaId, "-programs-vertical-nav")} setSelectedChildTabReactiveVar={handleTabSelect} isLoading={isLoading} loadingComponent={loadingComponent}/>
        </div>
      </uniform_web_1.Overlay>
    </div>);
}
