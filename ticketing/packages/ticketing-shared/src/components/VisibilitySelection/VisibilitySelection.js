"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VisibilitySelection = VisibilitySelection;
/* eslint-disable @typescript-eslint/no-explicit-any -- allows us to dynamically build the keys for entity types */
var uniform_web_1 = require("@hudl/uniform-web");
var VisibilitySelection_module_scss_1 = require("./VisibilitySelection.module.scss");
function VisibilitySelection(props) {
    var title = props.title, subtitle = props.subtitle, visibilityOptions = props.visibilityOptions, selectedVisibility = props.selectedVisibility, setVisibility = props.setVisibility;
    var onRadioChange = function (value) {
        setVisibility(value);
    };
    return (<div data-qa-id="visibility-selection-container">
      <div className={VisibilitySelection_module_scss_1.default.textContainer}>
        <uniform_web_1.Text className={VisibilitySelection_module_scss_1.default.headerText}>{title}</uniform_web_1.Text>
        <uniform_web_1.Text>{subtitle}</uniform_web_1.Text>
      </div>
      <uniform_web_1.RadioGroup onChange={onRadioChange} valueChecked={selectedVisibility} orientation="horizontal" className={VisibilitySelection_module_scss_1.default.radioGroupMobile} qaId="visibility-radio-group">
        {visibilityOptions.map(function (option) { return (<uniform_web_1.Radio value={option.value} label={option.label} qaId={"".concat(option.qaId).concat(selectedVisibility === option.value ? '-selected' : '')}/>); })}
      </uniform_web_1.RadioGroup>
    </div>);
}
