"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntityPropertyChangeNotificationModal = EntityPropertyChangeNotificationModal;
var uniform_web_1 = require("@hudl/uniform-web");
var frontends_i18n_1 = require("frontends-i18n");
function EntityPropertyChangeNotificationModal(props) {
    var isOpen = props.isOpen, header = props.header, notifications = props.notifications, qaId = props.qaId, onProceed = props.onProceed, closeModal = props.closeModal;
    return (<div>
      <uniform_web_1.Modal size="default" isOpen={isOpen} header={header} onClose={closeModal} qaId={qaId} showCancelAction={false} actions={[
            {
                buttonType: 'primary',
                qaId: "".concat(qaId, "-proceed-button"),
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing.event-time-change-modal.action.update' }),
                onPress: onProceed,
            },
            {
                buttonType: 'cancel',
                qaId: "".concat(qaId, "-cancel-button"),
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing.cancel' }),
                onPress: closeModal,
            },
        ]}>
        {notifications.map(function (notification, index) { return (<uniform_web_1.Text key={index} qaId={"".concat(qaId, "-notification-").concat(index + 1)}>
            {notification}
          </uniform_web_1.Text>); })}
      </uniform_web_1.Modal>
    </div>);
}
