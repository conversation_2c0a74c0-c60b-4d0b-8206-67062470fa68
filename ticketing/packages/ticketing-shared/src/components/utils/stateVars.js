"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.selectedFormStep = exports.isMobileScreen = exports.isMediaScreen = void 0;
var client_1 = require("@apollo/client");
var MaxTabletWidth = '768px';
var MaxMobileWidth = '480px';
exports.isMediaScreen = (0, client_1.makeVar)(window.matchMedia("(max-width: ".concat(MaxTabletWidth, ")")).matches);
window.matchMedia("(max-width: ".concat(MaxTabletWidth, ")")).addEventListener('change', function (e) { return (0, exports.isMediaScreen)(e.matches); });
exports.isMobileScreen = (0, client_1.makeVar)(window.matchMedia("(max-width: ".concat(MaxMobileWidth, ")")).matches);
window.matchMedia("(max-width: ".concat(MaxMobileWidth, ")")).addEventListener('change', function (e) { return (0, exports.isMobileScreen)(e.matches); });
exports.selectedFormStep = (0, client_1.makeVar)(0);
