"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var constants_1 = require("../../types/constants");
var QuantityInput_module_scss_1 = require("./QuantityInput.module.scss");
function QuantityInput(props) {
    var ticketTypeNotSet = props.ticketTypeNotSet, hasQuantityError = props.hasQuantityError, quantity = props.quantity, onChange = props.onChange, freeTicketingOnly = props.freeTicketingOnly, quantityLessThanExisting = props.quantityLessThanExisting;
    var isQuantityReadonly = !freeTicketingOnly && ticketTypeNotSet;
    var quantityInputQaId = "quantity-for-sale-input".concat(isQuantityReadonly ? '-disabled' : '').concat(hasQuantityError ? '-error' : '');
    var onQuantityChange = function (e) {
        if (e.target.value.length > constants_1.quantityInputMaxLength || e.target.value.includes('.')) {
            return;
        }
        var quantityValue = !Number.isNaN(parseInt(e.target.value, 10)) ? parseInt(e.target.value, 10) : undefined;
        onChange(quantityValue);
    };
    var getQuantityHelpText = function () {
        if (!freeTicketingOnly && ticketTypeNotSet) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.ticket-quantity-help-text' });
        }
        if (hasQuantityError) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.quantity-error-help-text' });
        }
        if (quantityLessThanExisting) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.quantity-less-than-existing' });
        }
        return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.quantity-help-text' });
    };
    return (<div className={QuantityInput_module_scss_1.default.wrapper}>
      <uniform_web_forms_legacy_1.Input type="number" className={QuantityInput_module_scss_1.default.flexInput} qaId={quantityInputQaId} value={quantity} hasError={hasQuantityError} helpText={getQuantityHelpText()} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.quantity-for-sale' })} onChange={function (e) {
            onQuantityChange(e);
        }} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.quantity-placeholder' })} isReadOnly={isQuantityReadonly}/>
    </div>);
}
exports.default = QuantityInput;
