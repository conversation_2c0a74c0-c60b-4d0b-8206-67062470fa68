"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var renderHelpers_1 = require("../../test/renderHelpers");
var QuantityInput_1 = require("./QuantityInput");
describe('Quantity Input Tests', function () {
    it('Renders quantity input for no ticket type selected', function () {
        (0, renderHelpers_1.renderWithOptions)(<QuantityInput_1.default quantity={undefined} ticketTypeNotSet hasQuantityError={false} onChange={vi.fn()} freeTicketingOnly={false} quantityLessThanExisting={false}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('quantity-for-sale-input-disabled')).toHaveAttribute('placeholder', 'Enter quantity');
        expect(react_1.screen.getByText('Select or add a new ticket type to set the quantity.')).toBeInTheDocument();
    });
    it('Renders quantity input when ticket type has quantity', function () {
        (0, renderHelpers_1.renderWithOptions)(<QuantityInput_1.default quantity={10} ticketTypeNotSet={false} hasQuantityError={false} onChange={vi.fn()} freeTicketingOnly={false} quantityLessThanExisting={false}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('quantity-for-sale-input')).toHaveValue(10);
        expect(react_1.screen.queryByText('Select or add a new ticket type to set the quantity.')).not.toBeInTheDocument();
    });
    it('Help text changes when quantity is lowered', function () {
        (0, renderHelpers_1.renderWithOptions)(<QuantityInput_1.default quantity={10} ticketTypeNotSet={false} hasQuantityError={false} onChange={vi.fn()} freeTicketingOnly={false} quantityLessThanExisting/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('quantity-for-sale-input')).toHaveValue(10);
        expect(react_1.screen.getByText('Quantity is less than the original amount. This ticket type may become sold out.')).toBeInTheDocument();
    });
    it('Error text shows when quantity is zero on non-published event', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<QuantityInput_1.default quantity={0} ticketTypeNotSet={false} hasQuantityError onChange={vi.fn()} freeTicketingOnly={false} quantityLessThanExisting={false}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.clear(react_1.screen.getByTestId('quantity-for-sale-input-error'))];
                case 1:
                    _a.sent();
                    expect(react_1.screen.getByText('Quantity must be greater than 0.')).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Calls on change when price is changed', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, onChange;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    onChange = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<QuantityInput_1.default quantity={10} ticketTypeNotSet={false} hasQuantityError={false} onChange={onChange} freeTicketingOnly={false} quantityLessThanExisting={false}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('quantity-for-sale-input')).toHaveValue(10);
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('quantity-for-sale-input'), '5')];
                case 1:
                    _a.sent();
                    expect(onChange).toHaveBeenCalledWith(105);
                    return [2 /*return*/];
            }
        });
    }); });
});
