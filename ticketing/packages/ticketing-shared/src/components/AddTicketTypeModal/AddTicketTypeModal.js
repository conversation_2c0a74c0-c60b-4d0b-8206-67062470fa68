"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var uniform_web_notifications_legacy_1 = require("@hudl/uniform-web-notifications-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var sharedEnums_1 = require("../../enums/sharedEnums");
var PriceInput_1 = require("../PriceInput/PriceInput");
var AddTicketTypeModal_module_scss_1 = require("./AddTicketTypeModal.module.scss");
function AddTicketTypeModal(props) {
    var _this = this;
    var _a, _b, _c;
    var isOpen = props.isOpen, closeModal = props.closeModal, providedTicketTypeName = props.providedTicketTypeName, venueConfig = props.venueConfig, createTicketType = props.createTicketType, freeTicketingOnly = props.freeTicketingOnly, showTicketTypeErrorToast = props.showTicketTypeErrorToast, setShowTicketTypeErrorToast = props.setShowTicketTypeErrorToast;
    var _d = (0, react_1.useState)(providedTicketTypeName !== null && providedTicketTypeName !== void 0 ? providedTicketTypeName : ''), ticketTypeName = _d[0], setTicketTypeName = _d[1];
    var ticketTypeNameMaxLength = 60;
    var ticketTypeNameLimitError = ticketTypeName.length > ticketTypeNameMaxLength;
    var _e = (0, react_1.useState)(0), ticketTypePrice = _e[0], setTicketTypePrice = _e[1];
    var _f = (0, react_1.useState)(false), savingTicketType = _f[0], setSavingTicketType = _f[1];
    var _g = (0, react_1.useState)([]), selectedCategories = _g[0], setSelectedCategories = _g[1];
    var isRetrying = (0, react_1.useRef)(false);
    (0, react_1.useEffect)(function () {
        setTicketTypeName(providedTicketTypeName !== null && providedTicketTypeName !== void 0 ? providedTicketTypeName : '');
    }, [providedTicketTypeName]);
    var onModalClose = (0, react_1.useCallback)(function () {
        setTicketTypeName('');
        setTicketTypePrice(0);
        closeModal();
        setSelectedCategories([]);
    }, [closeModal]);
    var addTicketType = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var ticketTypeInput;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (savingTicketType)
                        return [2 /*return*/];
                    ticketTypeInput = {
                        name: ticketTypeName,
                        priceInCents: ticketTypePrice,
                    };
                    setSavingTicketType(true);
                    return [4 /*yield*/, createTicketType(ticketTypeInput).then(function () {
                            onModalClose();
                        })];
                case 1:
                    _a.sent();
                    setSavingTicketType(false);
                    return [2 /*return*/];
            }
        });
    }); }, [createTicketType, onModalClose, savingTicketType, ticketTypeName, ticketTypePrice]);
    (0, react_1.useEffect)(function () {
        if (isRetrying.current && !showTicketTypeErrorToast) {
            addTicketType();
            isRetrying.current = false;
        }
    }, [isRetrying, showTicketTypeErrorToast, addTicketType]);
    var handleDismiss = (0, react_1.useCallback)(function () {
        setShowTicketTypeErrorToast(false);
    }, [setShowTicketTypeErrorToast]);
    var handleRetry = (0, react_1.useCallback)(function () {
        isRetrying.current = true;
    }, [isRetrying]);
    var saveButtonQaId = "".concat(ticketTypeName === '' ? '-disabled' : '');
    var categoryOptions = (_c = (_b = (_a = venueConfig === null || venueConfig === void 0 ? void 0 : venueConfig.seatingChart) === null || _a === void 0 ? void 0 : _a.seatsDotIoCategories) === null || _b === void 0 ? void 0 : _b.map(function (category) { return ({
        label: category.label,
        value: category.key,
    }); })) !== null && _c !== void 0 ? _c : [];
    return (<div>
      <uniform_web_1.Modal size="large" isOpen={isOpen} header={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.add-new-ticket-type' })} onClose={onModalClose} qaId="add-ticket-type-modal" actions={[
            {
                buttonType: 'primary',
                status: savingTicketType ? 'spinning' : undefined,
                qaId: "add-ticket-type-save-button".concat(saveButtonQaId),
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.save' }),
                isDisabled: ticketTypeName === '' || ticketTypeNameLimitError,
                onPress: function () {
                    addTicketType();
                },
            },
        ]}>
        <uniform_web_forms_legacy_1.Input qaId={"ticket-type-name-input".concat(ticketTypeNameLimitError ? '-error' : '')} value={ticketTypeName} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.ticket-type-modal.ticket-type-name' })} isRequired hasError={ticketTypeNameLimitError} onChange={function (e) { return setTicketTypeName(e.target.value); }} helpText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.ticket-type-modal.ticket-type-name-character-limit' }, { charactersUsed: ticketTypeName.length, ticketTypeNameMaxLength: ticketTypeNameMaxLength })}/>
        <PriceInput_1.PriceInput className={AddTicketTypeModal_module_scss_1.default.priceInput} currency={sharedEnums_1.Currency.USD} priceInCents={ticketTypePrice} onPriceChange={setTicketTypePrice} isDisabled={freeTicketingOnly} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.price' })} errorMessage={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.fee-strategy.pass-price-minimum' })}/>
        {venueConfig && (<>
            <uniform_web_1.Select className={AddTicketTypeModal_module_scss_1.default.categorySelect} isMulti label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type-modal.add-to-section' })} value={selectedCategories} onChange={function (newValue) { return setSelectedCategories(newValue); }} options={categoryOptions} closeMenuOnSelect={false} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type-modal.select-sections' })} helpText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type-modal.select-help-text' }, { venueConfigName: venueConfig.name })} qaId="add-ticket-types-to-section-select"/>
            <div className={AddTicketTypeModal_module_scss_1.default.publishedNoteContainer}>
              <uniform_web_1.Note className={AddTicketTypeModal_module_scss_1.default.publishedNote} qaId="add-ticket-type-to-section-note">
                {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type-modal.add-ticket-type-to-section' })}
              </uniform_web_1.Note>
            </div>
          </>)}
      </uniform_web_1.Modal>
      {showTicketTypeErrorToast && (<uniform_web_notifications_legacy_1.Toast type="critical" text={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type-modal.error-saving-ticket-type' })} qaId="error-saving-ticket-type" action={{
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.retry' }),
                qaId: 'error-saving-ticket-type-retry-button',
                onClick: handleRetry,
            }} onDismiss={handleDismiss}/>)}
    </div>);
}
exports.default = AddTicketTypeModal;
