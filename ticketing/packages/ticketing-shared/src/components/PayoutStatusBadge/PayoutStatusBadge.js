"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayoutStatusBadge = PayoutStatusBadge;
var uniform_web_1 = require("@hudl/uniform-web");
var PayoutStatusBadge_module_scss_1 = require("./PayoutStatusBadge.module.scss");
function PayoutStatusBadge(props) {
    var message = props.message, actionButton = props.actionButton, payoutStatus = props.payoutStatus, icon = props.icon;
    return (<div className={"".concat(PayoutStatusBadge_module_scss_1.default["payout".concat(payoutStatus, "Container")])} data-qa-id={"payout-status-".concat(payoutStatus, "-badge")}>
      <div className={PayoutStatusBadge_module_scss_1.default.textContainer}>
        {icon ? icon : <uniform_web_1.IconInformation />}
        {message}
      </div>
      {actionButton}
    </div>);
}
