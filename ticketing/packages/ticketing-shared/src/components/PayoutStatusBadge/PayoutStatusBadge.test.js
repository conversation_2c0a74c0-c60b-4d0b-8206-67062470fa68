"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var sharedEnums_1 = require("../../enums/sharedEnums");
var renderHelpers_1 = require("../../test/renderHelpers");
var PayoutStatusBadge_1 = require("./PayoutStatusBadge");
describe('PayoutStatusBadge', function () {
    var message = <div>hello</div>;
    it('Renders correctly: Complete status', function () {
        (0, renderHelpers_1.renderWithOptions)(<PayoutStatusBadge_1.PayoutStatusBadge message={message} payoutStatus={sharedEnums_1.PayoutStatus.Complete}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('payout-status-Complete-badge')).toBeInTheDocument();
    });
    it('Renders correctly: Incomplete status', function () {
        (0, renderHelpers_1.renderWithOptions)(<PayoutStatusBadge_1.PayoutStatusBadge message={message} payoutStatus={sharedEnums_1.PayoutStatus.Incomplete}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('payout-status-Incomplete-badge')).toBeInTheDocument();
    });
    it('Renders correctly: Needs Review status', function () {
        (0, renderHelpers_1.renderWithOptions)(<PayoutStatusBadge_1.PayoutStatusBadge message={message} payoutStatus={sharedEnums_1.PayoutStatus.NeedsReview}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('payout-status-NeedsReview-badge')).toBeInTheDocument();
    });
    it('Renders action button', function () {
        (0, renderHelpers_1.renderWithOptions)(<PayoutStatusBadge_1.PayoutStatusBadge message={message} payoutStatus={sharedEnums_1.PayoutStatus.NeedsReview} actionButton={<button data-qa-id="action-button">Action Button</button>}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('action-button')).toBeInTheDocument();
    });
    it('Renders different icon', function () {
        (0, renderHelpers_1.renderWithOptions)(<PayoutStatusBadge_1.PayoutStatusBadge message={message} payoutStatus={sharedEnums_1.PayoutStatus.NeedsReview} icon={<div data-qa-id="icon">Icon</div>}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('icon')).toBeInTheDocument();
    });
});
