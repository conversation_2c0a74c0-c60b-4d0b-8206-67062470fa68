"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewableInformation = ReviewableInformation;
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_button_legacy_1 = require("@hudl/uniform-web-button-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var qaIdUtils_1 = require("../../utils/qaIdUtils");
var stateVars_1 = require("../../utils/stateVars");
var ReviewableInformation_module_scss_1 = require("./ReviewableInformation.module.scss");
function ReviewableInformation(props) {
    var _a;
    var reviewItem = props.reviewItem;
    var qaIdTitle = (0, qaIdUtils_1.kebabLowercaseQaId)(reviewItem.title);
    var navigateToStep = function () {
        (0, stateVars_1.selectedFormStep)(reviewItem.formStep);
    };
    return (<div className={ReviewableInformation_module_scss_1.default.reviewableInformationContainer} data-qa-id={"review-item-".concat(qaIdTitle)}>
      <div className={ReviewableInformation_module_scss_1.default.infoHeader}>
        <uniform_web_1.ItemTitle data-qa-id={"review-item-title-".concat(qaIdTitle)}>{reviewItem.title}</uniform_web_1.ItemTitle>
        {!reviewItem.hideButton && (<uniform_web_button_legacy_1.Button buttonStyle="minimal" size="small" onClick={navigateToStep} qaId={"review-item-update-button-".concat(qaIdTitle)}>
            {(_a = reviewItem.buttonTitle) !== null && _a !== void 0 ? _a : (0, frontends_i18n_1.formatMessage)({ id: 'ticketing.update' })}
          </uniform_web_button_legacy_1.Button>)}
      </div>
      <div className={ReviewableInformation_module_scss_1.default.reviewItemsContainer}>
        {reviewItem.reviewableInformation.map(function (info) {
            var qaIdInfoTitle = (0, qaIdUtils_1.kebabLowercaseQaId)(info.title);
            return (<div key={info.title} data-qa-id={"reviewable-information-".concat(qaIdInfoTitle)}>
              <uniform_web_1.Text className={ReviewableInformation_module_scss_1.default.reviewItemTitle} qaId={"reviewable-information-title-".concat(qaIdInfoTitle)}>
                {info.title}
              </uniform_web_1.Text>
              <div className={ReviewableInformation_module_scss_1.default.reviewItemData} data-qa-id={"reviewable-information-data-".concat(qaIdInfoTitle)}>
                {info.data}
              </div>
            </div>);
        })}
      </div>
    </div>);
}
