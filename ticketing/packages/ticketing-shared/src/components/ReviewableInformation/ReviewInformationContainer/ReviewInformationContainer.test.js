"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../../test/renderHelpers");
var ReviewInformationContainer_1 = require("./ReviewInformationContainer");
describe('ReviewInformationContainer', function () {
    it('Renders the review item correctly', function () {
        var reviewItems = [
            {
                title: 'Pass Details',
                formStep: 1,
                reviewableInformation: [
                    {
                        title: 'Pass Name',
                        data: 'Test Pass',
                    },
                ],
            },
        ];
        (0, renderHelpers_1.renderWithOptions)(<ReviewInformationContainer_1.ReviewInformationContainer reviewItems={reviewItems}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('review-information-container')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('reviewable-information-title-pass-name')).toBeInTheDocument();
    });
});
