"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewInformationContainer = ReviewInformationContainer;
var ReviewableInformation_1 = require("../ReviewableInformation/ReviewableInformation");
var ReviewInformationContainer_module_scss_1 = require("./ReviewInformationContainer.module.scss");
function ReviewInformationContainer(props) {
    var reviewItems = props.reviewItems;
    return (<div className={ReviewInformationContainer_module_scss_1.default.reviewItemsContainer} data-qa-id="review-information-container">
      {reviewItems.map(function (reviewItem) { return (<ReviewableInformation_1.ReviewableInformation key={reviewItem.title} reviewItem={reviewItem}/>); })}
    </div>);
}
