"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var ticketingMockData_1 = require("../../mockData/ticketingMockData");
var renderHelpers_1 = require("../../test/renderHelpers");
var testHelpers_1 = require("../../test/testHelpers");
var constants_1 = require("../../types/constants");
var TicketTypesList_1 = require("./TicketTypesList");
var organizationId = 'schoolId';
vi.mock('react-router-dom', function () { return ({
    useParams: vi.fn(function () {
        return { organizationId: organizationId };
    }),
}); });
var ticketType = (0, ticketingMockData_1.aTicketType)({ name: 'Student Admission', id: 'testId', priceInCents: 1000 });
var ticketTypeWithOverrides = __assign(__assign({}, ticketType), { priceOverride: undefined, quantityOverride: undefined });
var ticketedEvent = (0, ticketingMockData_1.aTicketedEvent)();
vi.mock('@hudl/uniform-web', function () { return __awaiter(void 0, void 0, void 0, function () {
    var uniform;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, vi.importActual('@hudl/uniform-web')];
            case 1:
                uniform = _a.sent();
                return [2 /*return*/, __assign(__assign({}, uniform), { CreatableSelect: uniform.Select })];
        }
    });
}); });
describe('Ticket Types List tests', function () {
    it('Add ticket type selection button is hidden when free ticketing only enabled', function () {
        (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly selectedTicketTypes={[]} setSelectedTicketTypes={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.queryByTestId('add-ticket-type-selection-button;')).toBeFalsy();
    });
    it('Add ticket type selection button is shown when paid ticketing enabled', function () { return __awaiter(void 0, void 0, void 0, function () {
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={[]} setSelectedTicketTypes={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    _a = expect;
                    return [4 /*yield*/, react_1.screen.findByTestId('add-ticket-type-selection-button')];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Calls set selected ticket types when input value is changed.', function () { return __awaiter(void 0, void 0, void 0, function () {
        var setSelectedTicketTypes, user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    setSelectedTicketTypes = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={[constants_1.emptyTicketType]} setSelectedTicketTypes={setSelectedTicketTypes} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    user = user_event_1.default.setup({ delay: null });
                    expect(react_1.screen.getByTestId('ticket-type-row')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('type-input-select')).toBeInTheDocument();
                    return [4 /*yield*/, (0, testHelpers_1.selectSelectOption)('type-input-select', 'Student Admission', user)];
                case 1:
                    _a.sent();
                    expect(setSelectedTicketTypes).toHaveBeenCalled();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Price is updated when an override is set.', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, priceInput;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={[ticketTypeWithOverrides]} setSelectedTicketTypes={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    priceInput = react_1.screen.getByTestId('price-input');
                    return [4 /*yield*/, (0, testHelpers_1.clearPriceInput)(priceInput, user)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(priceInput, '40')];
                case 2:
                    _a.sent();
                    expect(react_1.screen.getByTestId('price-input')).toHaveValue('40.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Ticket Type is removed when delete icon is clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, selectedTicketTypes, setSelectedTicketTypes;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup();
                    selectedTicketTypes = [
                        __assign(__assign({}, ticketTypeWithOverrides), { id: 'testId1', name: 'testing1' }),
                        __assign(__assign({}, ticketTypeWithOverrides), { id: 'testId2', name: 'testing2' }),
                        __assign(__assign({}, ticketTypeWithOverrides), { id: 'testId3', name: 'testing3' }),
                    ];
                    setSelectedTicketTypes = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={selectedTicketTypes} setSelectedTicketTypes={setSelectedTicketTypes} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('ticket-type-row-testId1')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('ticket-type-row-testId2')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('ticket-type-row-testId3')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('remove-ticket-type-button-1')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('remove-ticket-type-button-2')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('remove-ticket-type-button-3')).toBeInTheDocument();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('remove-ticket-type-button-1'))];
                case 1:
                    _a.sent();
                    expect(setSelectedTicketTypes).toHaveBeenCalledWith([selectedTicketTypes[1], selectedTicketTypes[2]]);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Only ticket type cannot be removed', function () {
        var selectedTicketTypes = [__assign(__assign({}, ticketTypeWithOverrides), { id: 'testId1', name: 'testing1' })];
        (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={selectedTicketTypes} setSelectedTicketTypes={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('ticket-type-row-testId1')).toBeInTheDocument();
        expect(react_1.screen.queryByTestId('remove-ticket-type-button-1')).toBeFalsy();
    });
    it('First Ticket Type can be removed', function () {
        var selectedTicketTypes = [
            ticketTypeWithOverrides,
            __assign(__assign({}, ticketTypeWithOverrides), { id: 'testId2', name: 'testing' }),
        ];
        (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={selectedTicketTypes} setSelectedTicketTypes={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByText('testing')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('remove-ticket-type-button-2')).toBeInTheDocument();
    });
    it('Add ticket type button adds empty ticket type', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, setSelectedTicketTypes, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    user = user_event_1.default.setup();
                    setSelectedTicketTypes = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypesList_1.TicketTypesList ticketedEvent={ticketedEvent} ticketTypes={[ticketType]} organizationId={organizationId} freeTicketingOnly={false} selectedTicketTypes={[ticketTypeWithOverrides]} setSelectedTicketTypes={setSelectedTicketTypes} quantityError={{}} setQuantityError={vi.fn()} priceError={{}} createTicketType={vi.fn()} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} setIsTicketedEventModified={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.queryByText('Select ticket price option')).toBeFalsy();
                    _a = expect;
                    return [4 /*yield*/, react_1.screen.findByTestId('add-ticket-type-selection-button')];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toBeInTheDocument();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('add-ticket-type-selection-button'))];
                case 2:
                    _b.sent();
                    expect(setSelectedTicketTypes).toHaveBeenCalledWith([ticketTypeWithOverrides, constants_1.emptyTicketType]);
                    return [2 /*return*/];
            }
        });
    }); });
});
