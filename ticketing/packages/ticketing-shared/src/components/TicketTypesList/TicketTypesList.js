"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketTypesList = TicketTypesList;
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_button_legacy_1 = require("@hudl/uniform-web-button-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var TicketTypeInput_1 = require("../../components/TicketTypeInput/TicketTypeInput");
var sharedEnums_1 = require("../../enums/sharedEnums");
var constants_1 = require("../../types/constants");
var ticketTypeOverrideUtils_1 = require("../../utility/ticketTypeOverrideUtils");
var TicketTypesList_module_scss_1 = require("./TicketTypesList.module.scss");
function TicketTypesList(props) {
    var _this = this;
    var ticketedEvent = props.ticketedEvent, ticketTypes = props.ticketTypes, freeTicketingOnly = props.freeTicketingOnly, setSelectedTicketTypes = props.setSelectedTicketTypes, selectedTicketTypes = props.selectedTicketTypes, quantityError = props.quantityError, setQuantityError = props.setQuantityError, priceError = props.priceError, createTicketType = props.createTicketType, setIsTicketedEventModified = props.setIsTicketedEventModified, organizationId = props.organizationId, showTicketTypeErrorToast = props.showTicketTypeErrorToast, setShowTicketTypeErrorToast = props.setShowTicketTypeErrorToast;
    var isEdit = !!(ticketedEvent === null || ticketedEvent === void 0 ? void 0 : ticketedEvent.id) && (ticketedEvent === null || ticketedEvent === void 0 ? void 0 : ticketedEvent.eventStatus) !== sharedEnums_1.TicketedEventStatus.Draft;
    var ticketTypesForOrganizationWithOverrides = (0, ticketTypeOverrideUtils_1.buildTicketTypesWithOverrides)(ticketTypes, ticketedEvent === null || ticketedEvent === void 0 ? void 0 : ticketedEvent.ticketTypeReferences);
    var addTicketTypeInput = function () {
        setSelectedTicketTypes(__spreadArray(__spreadArray([], selectedTicketTypes, true), [constants_1.emptyTicketType], false));
    };
    var getTicketTypeWithOverride = function (ticketType) {
        if (freeTicketingOnly) {
            // empty ticket type must be passed to set quantity override
            return ticketType;
        }
        return ticketType.id ? ticketType : undefined;
    };
    var selectedTicketTypeIds = selectedTicketTypes.map(function (tt) { return tt.id; });
    var unselectedTicketTypes = (ticketTypesForOrganizationWithOverrides === null || ticketTypesForOrganizationWithOverrides === void 0 ? void 0 : ticketTypesForOrganizationWithOverrides.filter(function (tt) { return !selectedTicketTypeIds.includes(tt.id); })) || [];
    var ticketTypeOptions = unselectedTicketTypes === null || unselectedTicketTypes === void 0 ? void 0 : unselectedTicketTypes.map(function (tt) {
        var _a;
        return ({
            label: (_a = tt === null || tt === void 0 ? void 0 : tt.name) !== null && _a !== void 0 ? _a : '',
            value: tt === null || tt === void 0 ? void 0 : tt.id,
        });
    });
    var addSelectedTicketType = function (newTicketType, index) {
        var newState = __spreadArray([], selectedTicketTypes, true);
        newState[index] = newTicketType;
        setSelectedTicketTypes(newState);
    };
    var createTicketTypeWithInput = function (index) { return function (ticketTypeInput) { return __awaiter(_this, void 0, void 0, function () {
        var createInput, createdTicketType;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    createInput = __assign(__assign({}, ticketTypeInput), { organizationId: organizationId });
                    return [4 /*yield*/, createTicketType(createInput, index)];
                case 1:
                    createdTicketType = _a.sent();
                    addSelectedTicketType(createdTicketType, index);
                    return [2 /*return*/];
            }
        });
    }); }; };
    var removeSelectedTicketType = function (index) {
        var newState = __spreadArray([], selectedTicketTypes, true);
        newState.splice(index, 1);
        setSelectedTicketTypes(newState);
    };
    var updateSelectedTicketType = function (newTicketTypeId, index) {
        var updatedTicketType = ticketTypesForOrganizationWithOverrides === null || ticketTypesForOrganizationWithOverrides === void 0 ? void 0 : ticketTypesForOrganizationWithOverrides.find(function (tt) { return tt.id === newTicketTypeId; });
        var newState = __spreadArray([], selectedTicketTypes, true);
        newState[index] = updatedTicketType;
        setSelectedTicketTypes(newState);
    };
    var setPriceOnSelectedTicketType = function (ticketTypeId, price) {
        var selectedTypes = __spreadArray([], selectedTicketTypes, true);
        var ticketType = selectedTypes.find(function (tt) { return tt.id === ticketTypeId; });
        if (ticketType && price !== (ticketType === null || ticketType === void 0 ? void 0 : ticketType.priceInCents)) {
            ticketType.priceOverride = Number.isNaN(price) ? undefined : price;
        }
        else if (ticketType) {
            ticketType.priceOverride = undefined;
        }
        setSelectedTicketTypes(selectedTypes);
    };
    var setQuantityOnSelectedTicketType = function (ticketTypeId, quantity) {
        var selectedTypes = __spreadArray([], selectedTicketTypes, true);
        var ticketType = selectedTypes.find(function (tt) { return tt.id === ticketTypeId; });
        if (ticketType) {
            ticketType.quantityOverride = quantity;
        }
        setSelectedTicketTypes(selectedTypes);
    };
    return (<div data-qa-id="ticket-types-list">
      {selectedTicketTypes &&
            selectedTicketTypes.map(function (ticketType, index) {
                var _a;
                var ticketTypeWithOverrides = getTicketTypeWithOverride(ticketType);
                var ticketTypeExistsOnEvent = !!((_a = ticketedEvent === null || ticketedEvent === void 0 ? void 0 : ticketedEvent.ticketTypes) === null || _a === void 0 ? void 0 : _a.some(function (tt) { return tt.id === (ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id); })) &&
                    ticketedEvent.eventStatus !== sharedEnums_1.TicketedEventStatus.Draft;
                var ticketTypeSelectDisabled = freeTicketingOnly || ticketTypeExistsOnEvent;
                return (
                // eslint-disable-next-line react/no-array-index-key -- empty ticket type is not unique
                <div key={"".concat(ticketType.id, " ").concat(index)}>
              <TicketTypeInput_1.TicketTypeInput ticketTypeWithOverrides={ticketTypeWithOverrides} index={index} allowDeletion={selectedTicketTypes.length > 1} freeTicketingOnly={freeTicketingOnly} setIsTicketedEventModified={setIsTicketedEventModified || (function () { })} createTicketType={createTicketTypeWithInput(index)} updateTicketType={updateSelectedTicketType} removeTicketType={removeSelectedTicketType} quantityError={quantityError} setQuantityError={setQuantityError} setQuantityOnSelectedTicketType={setQuantityOnSelectedTicketType} priceError={priceError} setPriceOnSelectedTicketType={setPriceOnSelectedTicketType} isSelectDisabled={ticketTypeSelectDisabled} isTicketTypeOnEvent={ticketTypeExistsOnEvent} ticketTypeOptions={ticketTypeOptions} existingQuantity={ticketType.quantityOverride} isEdit={isEdit} showTicketTypeErrorToast={showTicketTypeErrorToast} setShowTicketTypeErrorToast={setShowTicketTypeErrorToast}/>
            </div>);
            })}
      {!freeTicketingOnly && (<uniform_web_button_legacy_1.Button qaId="add-ticket-type-selection-button" className={TicketTypesList_module_scss_1.default.addTicketTypeButton} onClick={addTicketTypeInput} buttonType="subtle" icon={<uniform_web_1.IconAdd />}>
          {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add' })}
        </uniform_web_button_legacy_1.Button>)}
    </div>);
}
