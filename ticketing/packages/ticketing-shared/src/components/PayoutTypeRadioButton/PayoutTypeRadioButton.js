"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayoutTypeRadioButton = PayoutTypeRadioButton;
var uniform_web_1 = require("@hudl/uniform-web");
var PayoutTypeRadioButton_module_scss_1 = require("./PayoutTypeRadioButton.module.scss");
function PayoutTypeRadioButton(props) {
    var setPayoutType = props.setPayoutType, label = props.label, value = props.value, isSelected = props.isSelected, subText = props.subText, containerClassName = props.containerClassName, isDisabled = props.isDisabled;
    var onPayoutTypeChange = function () {
        setPayoutType(value);
    };
    var disabledClassName = isDisabled ? PayoutTypeRadioButton_module_scss_1.default.disabled : '';
    return (<div onClick={onPayoutTypeChange} onKeyDown={onPayoutTypeChange} role="button" tabIndex={0} data-qa-id={"payout-type-".concat(value)} className={"".concat(PayoutTypeRadioButton_module_scss_1.default.payoutTypeRadioButtonContainer, " ").concat(containerClassName, " ").concat(disabledClassName)}>
      <uniform_web_1.Radio isChecked={isSelected} value={value} label={label} onChange={onPayoutTypeChange} className={PayoutTypeRadioButton_module_scss_1.default.radioButton} qaId={"payout-type-radio-button-".concat(value).concat(isSelected ? '-selected' : '').concat(isDisabled ? '-disabled' : '')}/>
      <div className={PayoutTypeRadioButton_module_scss_1.default.subText}>
        <uniform_web_1.Text>{subText}</uniform_web_1.Text>
      </div>
    </div>);
}
