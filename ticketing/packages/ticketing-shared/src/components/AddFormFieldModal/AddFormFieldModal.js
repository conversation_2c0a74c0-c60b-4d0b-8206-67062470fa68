"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var client_1 = require("@apollo/client");
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var uniform_web_notifications_legacy_1 = require("@hudl/uniform-web-notifications-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var sharedEnums_1 = require("../../enums/sharedEnums");
var encodingUtils_1 = require("../../utility/encodingUtils");
var stateVars_1 = require("../../utility/stateVars");
var AddFormFieldModal_module_scss_1 = require("./AddFormFieldModal.module.scss");
function AddFormFieldModal(props) {
    var _this = this;
    var organizationId = props.organizationId, createFormField = props.createFormField, showCreateFormFieldErrorToast = props.showCreateFormFieldErrorToast, setShowCreateFormFieldErrorToast = props.setShowCreateFormFieldErrorToast;
    var isOpen = props.isOpen, closeModal = props.closeModal;
    var isMobile = (0, client_1.useReactiveVar)(stateVars_1.isMediaScreen);
    var _a = (0, react_1.useState)(''), formFieldName = _a[0], setFormFieldName = _a[1];
    var _b = (0, react_1.useState)(''), formFieldHelpText = _b[0], setFormFieldHelpText = _b[1];
    var _c = (0, react_1.useState)(true), formFieldRequired = _c[0], setFormFieldRequired = _c[1];
    var formFieldNameMaxLength = 150;
    var formFieldNameHasError = formFieldName.length > formFieldNameMaxLength;
    var formFieldHelpTextMaxLength = 300;
    var formFieldHelpTextHasError = formFieldHelpText.length > formFieldHelpTextMaxLength;
    var canCreateFormField = formFieldName.length > 0 && !formFieldNameHasError && !formFieldHelpTextHasError;
    var _d = (0, react_1.useState)(false), creatingFormField = _d[0], setCreatingFormField = _d[1];
    var isRetrying = (0, react_1.useRef)(false);
    var FormFieldRequired;
    (function (FormFieldRequired) {
        FormFieldRequired["Yes"] = "Yes";
        FormFieldRequired["No"] = "No";
    })(FormFieldRequired || (FormFieldRequired = {}));
    var onModalClose = (0, react_1.useCallback)(function () {
        closeModal();
        setCreatingFormField(false);
        setFormFieldName('');
        setFormFieldHelpText('');
        setFormFieldRequired(true);
    }, [closeModal]);
    var addFormField = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var payload;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (creatingFormField)
                        return [2 /*return*/];
                    payload = {
                        organizationId: (0, encodingUtils_1.encodeOrganizationId)(organizationId),
                        label: formFieldName,
                        helpText: formFieldHelpText.length > 0 ? formFieldHelpText : undefined,
                        fieldType: sharedEnums_1.FormFieldType.Text,
                        isRequired: formFieldRequired,
                    };
                    setCreatingFormField(true);
                    return [4 /*yield*/, createFormField(payload).then(function () {
                            onModalClose();
                        })];
                case 1:
                    _a.sent();
                    setCreatingFormField(false);
                    return [2 /*return*/];
            }
        });
    }); }, [
        creatingFormField,
        organizationId,
        formFieldName,
        formFieldHelpText,
        formFieldRequired,
        createFormField,
        onModalClose,
    ]);
    (0, react_1.useEffect)(function () {
        if (isRetrying.current && !showCreateFormFieldErrorToast) {
            addFormField();
            isRetrying.current = false;
        }
    }, [isRetrying, showCreateFormFieldErrorToast, addFormField]);
    var handleDismiss = (0, react_1.useCallback)(function () {
        setShowCreateFormFieldErrorToast(false);
    }, [setShowCreateFormFieldErrorToast]);
    var handleRetry = (0, react_1.useCallback)(function () {
        isRetrying.current = true;
    }, [isRetrying]);
    var onRadioChange = function (value) {
        setFormFieldRequired(value === FormFieldRequired.Yes);
    };
    return (<div className={AddFormFieldModal_module_scss_1.default.addFormFieldModalContainer}>
      <uniform_web_1.Modal size="large" isOpen={isOpen} qaId="add-form-field-modal" header={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.header' })} onClose={onModalClose} actions={[
            {
                buttonType: 'primary',
                qaId: "add-form-field-modal-add-button".concat(!canCreateFormField ? '-disabled' : ''),
                status: creatingFormField ? 'spinning' : undefined,
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.add-button' }),
                isDisabled: !canCreateFormField,
                onPress: function () {
                    addFormField();
                },
            },
        ]}>
        <uniform_web_forms_legacy_1.Input value={formFieldName} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.name.label' })} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.name.placeholder' })} qaId={"add-form-field-modal-name-input".concat(formFieldNameHasError ? '-error' : '')} isRequired hasError={formFieldNameHasError} onChange={function (e) {
            setFormFieldName(e.target.value);
        }} helpText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.name.character-limit' }, { charactersUsed: formFieldName.length, fieldNameMaxLength: formFieldNameMaxLength })}/>
        <uniform_web_forms_legacy_1.Input className={AddFormFieldModal_module_scss_1.default.helpTextInput} value={formFieldHelpText} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.help-text.label' })} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.help-text.placeholder' })} qaId={"add-form-field-modal-help-text-input".concat(formFieldHelpTextHasError ? '-error' : '')} hasError={formFieldHelpTextHasError} onChange={function (e) {
            setFormFieldHelpText(e.target.value);
        }} helpText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.help-text.character-limit' }, { charactersUsed: formFieldHelpText.length, helpTextMaxLength: formFieldHelpTextMaxLength })}/>
        <uniform_web_1.Subhead className={AddFormFieldModal_module_scss_1.default.requiredHeader}>
          {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.required.label' })}
        </uniform_web_1.Subhead>
        <uniform_web_1.RadioGroup onChange={onRadioChange} valueChecked={formFieldRequired ? FormFieldRequired.Yes : FormFieldRequired.No} orientation={isMobile ? 'vertical' : 'horizontal'} qaId="add-form-field-modal-required-radio-input">
          <uniform_web_1.Radio value={FormFieldRequired.Yes} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.required.yes' })} qaId={"add-form-field-modal-required-yes".concat(formFieldRequired ? '-checked' : '')}/>
          <uniform_web_1.Radio value={FormFieldRequired.No} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.required.no' })} qaId={"add-form-field-modal-required-no".concat(!formFieldRequired ? '-checked' : '')}/>
        </uniform_web_1.RadioGroup>
      </uniform_web_1.Modal>
      {showCreateFormFieldErrorToast && (<uniform_web_notifications_legacy_1.Toast type="critical" qaId="add-form-field-modal-error-toast" text={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.form-fields.add-field-modal.toast.error' })} action={{
                text: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.retry' }),
                qaId: 'add-form-field-modal-error-toast-retry',
                onClick: handleRetry,
            }} onDismiss={handleDismiss}/>)}
    </div>);
}
exports.default = AddFormFieldModal;
