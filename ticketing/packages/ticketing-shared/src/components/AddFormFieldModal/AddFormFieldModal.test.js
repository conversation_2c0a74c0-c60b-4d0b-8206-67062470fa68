"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var testing_1 = require("@apollo/client/testing");
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var itFlaky_1 = require("@hudl/vitest-config/itFlaky");
var renderHelpers_1 = require("../../test/renderHelpers");
var AddFormFieldModal_1 = require("./AddFormFieldModal");
var closeModal = vi.fn();
var organizationId = 'OrgId';
beforeEach(function () {
    vi.resetAllMocks();
});
describe('AddFormFieldModal -- UI', function () {
    it('Renders modal in default state', function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<testing_1.MockedProvider mocks={[]}>
        <AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={vi.fn(function () { return Promise.resolve(); })} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>
      </testing_1.MockedProvider>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button-disabled')).toBeDisabled();
                    expect(react_1.screen.getByTestId('add-form-field-modal-name-input')).toHaveValue('');
                    expect(react_1.screen.getByTestId('add-form-field-modal-help-text-input')).toHaveValue('');
                    expect(react_1.screen.getByTestId('add-form-field-modal-required-radio-input')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('add-form-field-modal-required-yes-checked')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('add-form-field-modal-required-no')).toBeInTheDocument();
                    expect(react_1.screen.queryByTestId('add-form-field-modal-error-toast')).toBeFalsy();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Create button disabled if no name', function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<testing_1.MockedProvider mocks={[]}>
        <AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={vi.fn(function () { return Promise.resolve(); })} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>
      </testing_1.MockedProvider>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button-disabled')).toBeDisabled();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Create button enabled if name and no help text', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<testing_1.MockedProvider mocks={[]}>
        <AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={vi.fn(function () { return Promise.resolve(); })} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>
      </testing_1.MockedProvider>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-name-input'), 'Name')];
                case 2:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button')).not.toBeDisabled();
                    return [2 /*return*/];
            }
        });
    }); });
    (0, itFlaky_1.itFlaky)('Create button disabled and input error if name too long', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<testing_1.MockedProvider mocks={[]}>
        <AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={vi.fn(function () { return Promise.resolve(); })} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>
      </testing_1.MockedProvider>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('add-form-field-modal-name-input'))];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.paste('long name'.repeat(50))];
                case 3:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-name-input-error')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button-disabled')).toBeDisabled();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Create button disabled and input error if help text too long', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<testing_1.MockedProvider mocks={[]}>
          <AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={vi.fn(function () { return Promise.resolve(); })} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>
        </testing_1.MockedProvider>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-name-input'), 'Name')];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-help-text-input'), 'a'.repeat(301))];
                case 3:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-name-input')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('add-form-field-modal-help-text-input-error')).toBeInTheDocument();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button-disabled')).toBeDisabled();
                    return [2 /*return*/];
            }
        });
    }); }, 
    // Timeout for the test has been increased as we were seeing timeouts due to the long `.type` command
    // We tried using `.paste` instead but found it flaky on CI
    { timeout: 10000 });
});
describe('AddFormFieldModal -- GraphQL', function () {
    it('Pressing Create calls Mutation - All Fields', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, label, helpText, createMock;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    label = 'A Label';
                    helpText = 'Help Text';
                    createMock = vi.fn(function () { return Promise.resolve(); });
                    (0, renderHelpers_1.renderWithOptions)(<AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={createMock} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-name-input'), label)];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-help-text-input'), helpText)];
                case 3:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button')).toBeEnabled();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('add-form-field-modal-add-button'))];
                case 4:
                    _a.sent();
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(createMock).toHaveBeenCalled(); })];
                case 5:
                    _a.sent();
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(closeModal).toHaveBeenCalled(); })];
                case 6:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Pressing Create calls Mutation - No Help Text', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, label, createMock;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    label = 'A Label';
                    createMock = vi.fn(function () { return Promise.resolve(); });
                    (0, renderHelpers_1.renderWithOptions)(<AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={createMock} showCreateFormFieldErrorToast={false} setShowCreateFormFieldErrorToast={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('add-form-field-modal-name-input'), label)];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('add-form-field-modal-required-no'))];
                case 3:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-add-button')).toBeEnabled();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('add-form-field-modal-add-button'))];
                case 4:
                    _a.sent();
                    expect(createMock).toHaveBeenCalled();
                    expect(closeModal).toHaveBeenCalled();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Shows error toast when showCreateFormFieldErrorToast is true', function () { return __awaiter(void 0, void 0, void 0, function () {
        var createMock;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    createMock = vi.fn(function () { return Promise.resolve(); });
                    (0, renderHelpers_1.renderWithOptions)(<AddFormFieldModal_1.default isOpen closeModal={closeModal} organizationId={organizationId} createFormField={createMock} showCreateFormFieldErrorToast setShowCreateFormFieldErrorToast={vi.fn()}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(react_1.screen.getByTestId('add-form-field-modal')).toBeInTheDocument(); })];
                case 1:
                    _a.sent();
                    expect(react_1.screen.getByTestId('add-form-field-modal-error-toast')).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
});
