"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TicketTypeInput = TicketTypeInput;
var react_1 = require("react");
var client_1 = require("@apollo/client");
var classnames_1 = require("classnames");
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_button_legacy_1 = require("@hudl/uniform-web-button-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var stateVars_1 = require("../../utility/stateVars");
var ticketTypeOverrideUtils_1 = require("../../utility/ticketTypeOverrideUtils");
var AddTicketTypeModal_1 = require("../AddTicketTypeModal/AddTicketTypeModal");
var QuantityInput_1 = require("../QuantityInput/QuantityInput");
var CreateNewLabel_1 = require("../selectComponents/CreateNewLabel/CreateNewLabel");
var NoOptionsMessage_1 = require("../selectComponents/NoOptionsMessage/NoOptionsMessage");
var TicketTypePriceInput_1 = require("../TicketTypePriceInput/TicketTypePriceInput");
var TicketTypeInput_module_scss_1 = require("./TicketTypeInput.module.scss");
function TicketTypeInput(props) {
    var _a;
    var _b, _c;
    var existingQuantity = props.existingQuantity, ticketTypeWithOverrides = props.ticketTypeWithOverrides, index = props.index, allowDeletion = props.allowDeletion, freeTicketingOnly = props.freeTicketingOnly, setIsTicketedEventModified = props.setIsTicketedEventModified, createTicketType = props.createTicketType, updateTicketType = props.updateTicketType, removeTicketType = props.removeTicketType, quantityError = props.quantityError, setQuantityError = props.setQuantityError, setQuantityOnSelectedTicketType = props.setQuantityOnSelectedTicketType, priceError = props.priceError, setPriceOnSelectedTicketType = props.setPriceOnSelectedTicketType, isSelectDisabled = props.isSelectDisabled, isTicketTypeOnEvent = props.isTicketTypeOnEvent, ticketTypeOptions = props.ticketTypeOptions, isEdit = props.isEdit, showTicketTypeErrorToast = props.showTicketTypeErrorToast, setShowTicketTypeErrorToast = props.setShowTicketTypeErrorToast;
    var isMobile = (0, client_1.useReactiveVar)(stateVars_1.isMobileScreen);
    var _d = (0, react_1.useState)(false), tooltipOpen = _d[0], setTooltipOpen = _d[1];
    var _e = (0, react_1.useState)(false), quantityLessThanExisting = _e[0], setQuantityLessThanExisting = _e[1];
    var _f = (0, react_1.useState)(false), isAddTicketTypeModalOpen = _f[0], setIsAddTicketTypeModalOpen = _f[1];
    var _g = (0, react_1.useState)(''), providedTicketTypeName = _g[0], setProvidedTicketTypeName = _g[1];
    var ticketTypeValue = ticketTypeWithOverrides && {
        label: ticketTypeWithOverrides.name,
        value: ticketTypeWithOverrides.id,
    };
    var freeTicketingValue = {
        label: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.type.hardcoded-free-admission' }),
        value: (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.type.hardcoded-free-admission' }),
    };
    var handleRemoveTicketType = function () {
        removeTicketType(index);
    };
    var onTicketTypeChange = function (ticketTypeOption) {
        updateTicketType(ticketTypeOption.value, index);
    };
    var openAddNewTicketTypeModalWithName = function (ticketTypeName) {
        setProvidedTicketTypeName(ticketTypeName);
        setIsAddTicketTypeModalOpen(true);
    };
    var ticketTypeSelectionClassNames = (0, classnames_1.default)(TicketTypeInput_module_scss_1.default.ticketTypeSelection, (_a = {},
        _a[TicketTypeInput_module_scss_1.default.ticketTypeSelectionMargin] = index === 0,
        _a[TicketTypeInput_module_scss_1.default.ticketTypeSelectionMarginFreeOnly] = freeTicketingOnly,
        _a));
    var onTicketTypePriceChange = function (newPriceInCents) {
        var _a;
        setIsTicketedEventModified(true);
        setPriceOnSelectedTicketType((_a = ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) !== null && _a !== void 0 ? _a : '', newPriceInCents);
    };
    var isQuantityLessThanExisting = function (quantityValue) {
        if (quantityValue === undefined) {
            return false;
        }
        return existingQuantity || existingQuantity === 0 ? quantityValue < existingQuantity : !Number.isNaN(quantityValue);
    };
    var setTicketTypeQuantity = function (quantityValue) {
        var _a;
        if (quantityValue === (ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.quantityOverride)) {
            return;
        }
        setQuantityOnSelectedTicketType((_a = ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) !== null && _a !== void 0 ? _a : '', quantityValue);
    };
    var onTicketTypeQuantityChange = function (newQuantity) {
        var _a;
        setTicketTypeQuantity(newQuantity);
        var quantityValueLessThanExisting = false;
        if (isTicketTypeOnEvent) {
            quantityValueLessThanExisting = isQuantityLessThanExisting(newQuantity);
            setQuantityLessThanExisting(quantityValueLessThanExisting);
        }
        var errorWithQuantity = (!isEdit && newQuantity === 0) || (!!newQuantity && (Number.isNaN(newQuantity) || newQuantity <= 0));
        ticketTypeWithOverrides && setQuantityError(__assign(__assign({}, quantityError), (_a = {}, _a[ticketTypeWithOverrides.id] = errorWithQuantity, _a)));
        setIsTicketedEventModified(true);
    };
    var getNoOptionsMessage = function () {
        return (<NoOptionsMessage_1.NoOptionsMessage message={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.no-ticket-type-options-message' })} qaId="no-ticket-type-options-message"/>);
    };
    var getCreateNewLabel = function (ticketTypeName) {
        return (<CreateNewLabel_1.CreateNewLabel newOptionText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.add-new-ticket-type-create-prompt' }, { ticketTypeName: ticketTypeName })} footerText={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.add-new-ticket-type-helper' })} qaId="create-new-ticket-type"/>);
    };
    var getTicketTypeSelectLabel = function () {
        if (isSelectDisabled) {
            return (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type.label' });
        }
        return (<>
        {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticket-type.label' })}
        <uniform_web_1.Tooltip content={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.ticket-type-tooltip' })} isOpen={tooltipOpen} qaId="ticket-type-info-tooltip">
          <span className={TicketTypeInput_module_scss_1.default.infoIconContainer} onMouseEnter={function () { return setTooltipOpen(true); }} onMouseLeave={function () { return setTooltipOpen(false); }}>
            <uniform_web_1.IconInformation size="small" title={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.ticket-type-info-title' })} className={TicketTypeInput_module_scss_1.default.infoIcon} qaId="ticket-type-info-icon"/>
          </span>
        </uniform_web_1.Tooltip>
      </>);
    };
    var renderDeleteIcon = function () { return (<div className={TicketTypeInput_module_scss_1.default.ticketTypeButtonsContainer}>
      <div className={TicketTypeInput_module_scss_1.default.addNewTicketTypeButtonContainerCenter}>
        <uniform_web_button_legacy_1.Button qaId={"remove-ticket-type-button-".concat(index + 1)} buttonType="subtle" icon={<uniform_web_1.IconDelete />} onClick={handleRemoveTicketType}/>
      </div>
    </div>); };
    return (<div className={ticketTypeSelectionClassNames} data-qa-id={"ticket-type-row".concat((ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) ? "-".concat(ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) : '')}>
      <div className={index !== 0 ? TicketTypeInput_module_scss_1.default.selectWrapper : TicketTypeInput_module_scss_1.default.selectWrapperNoButton}>
        <uniform_web_1.CreatableSelect captureMenuScroll={false} className={TicketTypeInput_module_scss_1.default.ticketTypeSelect} qaId={"type-input".concat(isSelectDisabled ? '-disabled' : '')} label={getTicketTypeSelectLabel()} onChange={function (newValue) { return onTicketTypeChange(newValue); }} value={freeTicketingOnly ? freeTicketingValue : ticketTypeValue} options={ticketTypeOptions} placeholder={ticketTypeValue || freeTicketingOnly
            ? undefined
            : (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.add-ticketing-page.select-ticket-price-option' })} onCreateOption={openAddNewTicketTypeModalWithName} formatCreateLabel={function (ticketTypeName) { return getCreateNewLabel(ticketTypeName); }} isRequired isReadOnly={isSelectDisabled} components={{ NoOptionsMessage: getNoOptionsMessage }}/>
        {isMobile && !isTicketTypeOnEvent && allowDeletion && renderDeleteIcon()}
      </div>
      <TicketTypePriceInput_1.default ticketTypeNotSet={!ticketTypeValue} hasPriceError={priceError[(_b = ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) !== null && _b !== void 0 ? _b : '']} freeTicketingOnly={freeTicketingOnly} price={(0, ticketTypeOverrideUtils_1.getPriceOfTicketTypeWithOverrides)(ticketTypeWithOverrides)} onChange={onTicketTypePriceChange}/>
      <QuantityInput_1.default ticketTypeNotSet={!ticketTypeValue} hasQuantityError={quantityError[(_c = ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.id) !== null && _c !== void 0 ? _c : '']} freeTicketingOnly={freeTicketingOnly} quantity={ticketTypeWithOverrides === null || ticketTypeWithOverrides === void 0 ? void 0 : ticketTypeWithOverrides.quantity} onChange={onTicketTypeQuantityChange} quantityLessThanExisting={quantityLessThanExisting}/>
      {!isMobile && !isTicketTypeOnEvent && allowDeletion && renderDeleteIcon()}
      <AddTicketTypeModal_1.default isOpen={isAddTicketTypeModalOpen} closeModal={function () { return setIsAddTicketTypeModalOpen(false); }} createTicketType={createTicketType} freeTicketingOnly={freeTicketingOnly} providedTicketTypeName={providedTicketTypeName} showTicketTypeErrorToast={showTicketTypeErrorToast} setShowTicketTypeErrorToast={setShowTicketTypeErrorToast}/>
    </div>);
}
