"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var ticketingMockData_1 = require("../../mockData/ticketingMockData");
var renderHelpers_1 = require("../../test/renderHelpers");
var testHelpers_1 = require("../../test/testHelpers");
var TicketTypeInput_1 = require("./TicketTypeInput");
vi.mock('@hudl/uniform-web', function () { return __awaiter(void 0, void 0, void 0, function () {
    var uniform;
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, vi.importActual('@hudl/uniform-web')];
            case 1:
                uniform = _a.sent();
                return [2 /*return*/, __assign(__assign({}, uniform), { CreatableSelect: uniform.Select })];
        }
    });
}); });
describe('Ticket Type Input tests', function () {
    it('Renders free ticket type when free ticketing only enabled', function () { return __awaiter(void 0, void 0, void 0, function () {
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={undefined} freeTicketingOnly setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('price-input-read-only')).toHaveValue('0.00');
                    _a = expect;
                    return [4 /*yield*/, (0, react_1.within)(react_1.screen.getByTestId('type-input-disabled-select')).findByText('Free Admission')];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Renders passed in ticket type when paid ticketing enabled', function () { return __awaiter(void 0, void 0, void 0, function () {
        var ticketTypeWithOverrides, _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ id: 'testTicketTypeIdNotOnEvent', priceInCents: 0 })), { priceOverride: undefined, quantityOverride: undefined });
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('price-input')).toHaveValue('0.00');
                    _a = expect;
                    return [4 /*yield*/, (0, react_1.within)(react_1.screen.getByTestId('type-input-select')).findByText(ticketTypeWithOverrides.name)];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Renders empty ticket type when paid ticketing enabled', function () { return __awaiter(void 0, void 0, void 0, function () {
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={undefined} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('price-input-read-only')).toHaveValue('0.00');
                    expect(react_1.screen.getByText('Select or add a new ticket type to set the price.')).toBeInTheDocument();
                    _a = expect;
                    return [4 /*yield*/, (0, react_1.within)(react_1.screen.getByTestId('type-input-select')).findByText('Select ticket price option')];
                case 1:
                    _a.apply(void 0, [_b.sent()]).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('UpdateTicketType called when option selected', function () { return __awaiter(void 0, void 0, void 0, function () {
        var updateTicketType, ticketTypeOptions, user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    updateTicketType = vi.fn();
                    ticketTypeOptions = [{ label: 'General Admission', value: 'testId' }];
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={undefined} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={updateTicketType} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={ticketTypeOptions} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    user = user_event_1.default.setup({ delay: null });
                    return [4 /*yield*/, (0, testHelpers_1.selectSelectOption)('type-input-select', 'General Admission', user)];
                case 1:
                    _a.sent();
                    expect(updateTicketType).toHaveBeenCalledWith('testId', 0);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Renders prompt to start typing when no options are provided', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={undefined} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('type-input-select'))];
                case 1:
                    _a.sent();
                    expect(react_1.screen.getByRole('heading', { name: 'Start typing to add a new ticket type.' })).toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Displays error message when quantity error is true', function () {
        var _a;
        var ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: undefined });
        var quantityError = (_a = {}, _a[ticketTypeWithOverrides.id] = true, _a);
        (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={quantityError} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByText('Quantity must be greater than 0.')).toBeInTheDocument();
    });
    it('Displays error message when price error is true', function () {
        var _a;
        var ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: undefined });
        var priceError = (_a = {}, _a[ticketTypeWithOverrides.id] = true, _a);
        (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={priceError} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
            withIntlProvider: true,
        });
        react_1.screen.debug(undefined, 100000);
        expect(react_1.screen.getByText('Set price to a minimum of $2 if your organization is covering fees.')).toBeInTheDocument();
    });
    it('Quantity updates called when quantity is edited to lower than existing set', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, ticketTypeWithOverrides, setQuantityOnSelectedTicketType, setQuantityError, setIsTicketedEventModified;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: 100 });
                    setQuantityOnSelectedTicketType = vi.fn();
                    setQuantityError = vi.fn();
                    setIsTicketedEventModified = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={setIsTicketedEventModified} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={setQuantityError} setQuantityOnSelectedTicketType={setQuantityOnSelectedTicketType} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent ticketTypeOptions={[]} existingQuantity={2} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.clear(react_1.screen.getByTestId('quantity-for-sale-input'))];
                case 1:
                    _b.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('quantity-for-sale-input'), '1')];
                case 2:
                    _b.sent();
                    expect(setQuantityOnSelectedTicketType).toHaveBeenCalledWith(ticketTypeWithOverrides.id, 1);
                    expect(setQuantityError).toHaveBeenCalledWith((_a = {}, _a[ticketTypeWithOverrides.id] = false, _a));
                    expect(setIsTicketedEventModified).toHaveBeenCalledWith(true);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Quantity error set to true when quantity is set to 0 on initial event', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, ticketTypeWithOverrides, setQuantityOnSelectedTicketType, setQuantityError, setIsTicketedEventModified;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: 100 });
                    setQuantityOnSelectedTicketType = vi.fn();
                    setQuantityError = vi.fn();
                    setIsTicketedEventModified = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={setIsTicketedEventModified} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={setQuantityError} setQuantityOnSelectedTicketType={setQuantityOnSelectedTicketType} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent ticketTypeOptions={[]} existingQuantity={2} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.clear(react_1.screen.getByTestId('quantity-for-sale-input'))];
                case 1:
                    _b.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('quantity-for-sale-input'), '0')];
                case 2:
                    _b.sent();
                    expect(setQuantityError).toHaveBeenCalledWith((_a = {}, _a[ticketTypeWithOverrides.id] = true, _a));
                    return [2 /*return*/];
            }
        });
    }); });
    it('Quantity error set to false when quantity is set to 0 on edited event', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, ticketTypeWithOverrides, setQuantityOnSelectedTicketType, setQuantityError, setIsTicketedEventModified;
        var _a;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: 100 });
                    setQuantityOnSelectedTicketType = vi.fn();
                    setQuantityError = vi.fn();
                    setIsTicketedEventModified = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={setIsTicketedEventModified} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={setQuantityError} setQuantityOnSelectedTicketType={setQuantityOnSelectedTicketType} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent ticketTypeOptions={[]} existingQuantity={2} isEdit showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.clear(react_1.screen.getByTestId('quantity-for-sale-input'))];
                case 1:
                    _b.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('quantity-for-sale-input'), '0')];
                case 2:
                    _b.sent();
                    expect(setQuantityError).toHaveBeenCalledWith((_a = {}, _a[ticketTypeWithOverrides.id] = false, _a));
                    return [2 /*return*/];
            }
        });
    }); });
    it('Price updates called when price changes', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, ticketTypeWithOverrides, setPriceOnSelectedTicketType, setIsTicketedEventModified, priceInput;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 })), { priceOverride: undefined, quantityOverride: 100 });
                    setPriceOnSelectedTicketType = vi.fn();
                    setIsTicketedEventModified = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={setIsTicketedEventModified} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={setPriceOnSelectedTicketType} isSelectDisabled={false} isTicketTypeOnEvent ticketTypeOptions={[]} existingQuantity={2} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    priceInput = react_1.screen.getByTestId('price-input');
                    return [4 /*yield*/, (0, testHelpers_1.clearPriceInput)(priceInput, user)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(priceInput, '5')];
                case 2:
                    _a.sent();
                    expect(setPriceOnSelectedTicketType).toHaveBeenCalledWith(ticketTypeWithOverrides.id, 500);
                    expect(setIsTicketedEventModified).toHaveBeenCalledWith(true);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Quantity can be edited to higher than what exists', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, ticketTypeWithOverrides;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    ticketTypeWithOverrides = __assign(__assign({}, (0, ticketingMockData_1.aTicketType)({ id: 'ticketTypeId', priceInCents: 100 })), { priceOverride: undefined, quantityOverride: 100 });
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    return [4 /*yield*/, user.clear(react_1.screen.getByTestId('quantity-for-sale-input'))];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(react_1.screen.getByTestId('quantity-for-sale-input'), '1000')];
                case 2:
                    _a.sent();
                    expect(react_1.screen.queryByText('Quantity is less than the original amount. This ticket type may become sold out.')).toBeFalsy();
                    return [2 /*return*/];
            }
        });
    }); });
    it('Delete icon is not shown when allow deletion is set to false', function () {
        var ticketType = (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 });
        var ticketTypeWithOverrides = __assign(__assign({}, ticketType), { priceOverride: undefined, quantityOverride: undefined });
        (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion={false} ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.queryByTestId('remove-ticket-type-button')).toBeFalsy();
    });
    it('Delete icon is shown when allow deletion is set to true', function () {
        var ticketType = (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 });
        var ticketTypeWithOverrides = __assign(__assign({}, ticketType), { priceOverride: undefined, quantityOverride: undefined });
        (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('remove-ticket-type-button-1')).toBeInTheDocument();
    });
    it('Remove ticket type is called when delete icon clicked', function () { return __awaiter(void 0, void 0, void 0, function () {
        var ticketType, ticketTypeWithOverrides, removeTicketType, user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    ticketType = (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 });
                    ticketTypeWithOverrides = __assign(__assign({}, ticketType), { priceOverride: undefined, quantityOverride: undefined });
                    removeTicketType = vi.fn();
                    user = user_event_1.default.setup({ delay: null });
                    (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={removeTicketType} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled={false} isTicketTypeOnEvent={false} ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByTestId('remove-ticket-type-button-1')).toBeInTheDocument();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('remove-ticket-type-button-1'))];
                case 1:
                    _a.sent();
                    expect(removeTicketType).toHaveBeenCalledWith(0);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Ticket type input is readonly when ticket type already exists on the event', function () {
        var ticketType = (0, ticketingMockData_1.aTicketType)({ priceInCents: 100 });
        var ticketTypeWithOverrides = __assign(__assign({}, ticketType), { priceOverride: undefined, quantityOverride: undefined });
        (0, renderHelpers_1.renderWithOptions)(<TicketTypeInput_1.TicketTypeInput allowDeletion ticketTypeWithOverrides={ticketTypeWithOverrides} freeTicketingOnly={false} setIsTicketedEventModified={vi.fn()} createTicketType={vi.fn()} updateTicketType={vi.fn()} removeTicketType={vi.fn()} quantityError={{}} setQuantityError={vi.fn()} setQuantityOnSelectedTicketType={vi.fn()} priceError={{}} setPriceOnSelectedTicketType={vi.fn()} isSelectDisabled isTicketTypeOnEvent ticketTypeOptions={[]} existingQuantity={undefined} isEdit={false} showTicketTypeErrorToast={false} setShowTicketTypeErrorToast={vi.fn()} index={0}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('type-input-disabled')).toBeInTheDocument();
        expect(react_1.screen.queryByTestId('ticket-type-info-tooltip')).toBeFalsy();
    });
});
