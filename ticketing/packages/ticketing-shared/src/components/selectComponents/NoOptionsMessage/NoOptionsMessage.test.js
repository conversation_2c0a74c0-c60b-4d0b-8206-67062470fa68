"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../../test/renderHelpers");
var NoOptionsMessage_1 = require("./NoOptionsMessage");
describe('NoOptionsMessage', function () {
    it('should render the provided message', function () {
        var message = 'No Options';
        var qaId = 'no-options-message';
        (0, renderHelpers_1.renderWithOptions)(<NoOptionsMessage_1.NoOptionsMessage message={message} qaId={qaId}/>);
        expect(react_1.screen.getByTestId(qaId)).toHaveTextContent(message);
    });
});
