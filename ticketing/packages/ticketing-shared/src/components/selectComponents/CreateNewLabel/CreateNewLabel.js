"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateNewLabel = CreateNewLabel;
var uniform_web_1 = require("@hudl/uniform-web");
var CreateNewLabel_module_scss_1 = require("./CreateNewLabel.module.scss");
function CreateNewLabel(_a) {
    var newOptionText = _a.newOptionText, footerText = _a.footerText, qaId = _a.qaId;
    var qaIdPrefix = qaId ? "".concat(qaId, "-") : '';
    return (<div data-qa-id={"".concat(qaIdPrefix, "create-new-label")}>
      <uniform_web_1.Text className={CreateNewLabel_module_scss_1.default.createNewOptionLabel} qaId={"".concat(qaIdPrefix, "new-option-text")}>
        {newOptionText}
      </uniform_web_1.Text>
      <h5 className={CreateNewLabel_module_scss_1.default.footerMessage} data-qa-id={"".concat(qaIdPrefix, "footer-text")}>
        {footerText}
      </h5>
    </div>);
}
