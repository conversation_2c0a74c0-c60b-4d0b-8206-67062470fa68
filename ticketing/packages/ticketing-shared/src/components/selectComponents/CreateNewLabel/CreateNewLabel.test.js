"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../../test/renderHelpers");
var CreateNewLabel_1 = require("./CreateNewLabel");
describe('CreateNewLabel', function () {
    it('should render a container for the provided text', function () {
        (0, renderHelpers_1.renderWithOptions)(<CreateNewLabel_1.CreateNewLabel newOptionText="New Option" footerText="Footer Text"/>);
        expect(react_1.screen.getByTestId('create-new-label')).toBeVisible();
    });
    it('should render the new option text', function () {
        var newOptionText = 'New Option';
        (0, renderHelpers_1.renderWithOptions)(<CreateNewLabel_1.CreateNewLabel newOptionText={newOptionText} footerText="Footer Text"/>);
        expect(react_1.screen.getByTestId('new-option-text')).toHaveTextContent(newOptionText);
    });
    it('should render the footer text', function () {
        var footerText = 'Footer Text';
        (0, renderHelpers_1.renderWithOptions)(<CreateNewLabel_1.CreateNewLabel newOptionText="New Option" footerText={footerText}/>);
        expect(react_1.screen.getByTestId('footer-text')).toHaveTextContent(footerText);
    });
    it('should build the correct QA IDs', function () {
        var qaId = 'my-qa-id';
        (0, renderHelpers_1.renderWithOptions)(<CreateNewLabel_1.CreateNewLabel newOptionText="New Option" footerText="Footer Text" qaId={qaId}/>);
        expect(react_1.screen.getByTestId("".concat(qaId, "-create-new-label"))).toBeVisible();
        expect(react_1.screen.getByTestId("".concat(qaId, "-new-option-text"))).toBeVisible();
        expect(react_1.screen.getByTestId("".concat(qaId, "-footer-text"))).toBeVisible();
    });
});
