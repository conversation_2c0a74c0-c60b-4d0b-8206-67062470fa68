"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isFormStepClickable = exports.useFormStepNavigation = exports.useIsNextFormStepReview = void 0;
var client_1 = require("@apollo/client");
var stateVars_1 = require("../../utils/stateVars");
var useIsLastStepSelected = function (formSteps) {
    var lastFormStep = formSteps.length - 1;
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return selectedStep === lastFormStep;
};
var useContinueToNextStep = function (formSteps) {
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return function () {
        if (formSteps[selectedStep + 1] !== undefined) {
            (0, stateVars_1.selectedFormStep)(selectedStep + 1);
        }
        else {
            (0, stateVars_1.selectedFormStep)(selectedStep);
        }
    };
};
var useGoBackToPreviousStep = function (formSteps) {
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return function () {
        if (formSteps[selectedStep - 1] !== undefined) {
            (0, stateVars_1.selectedFormStep)(selectedStep - 1);
        }
        else {
            (0, stateVars_1.selectedFormStep)(selectedStep);
        }
    };
};
var useCanGoBackStep = function (formSteps) {
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return formSteps[0] && formSteps[selectedStep] !== formSteps[0];
};
var useCanGoForwardStep = function (formSteps) {
    var _a;
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return (_a = formSteps[selectedStep]) === null || _a === void 0 ? void 0 : _a.formValid;
};
var useIsNextFormStepReview = function (formSteps) {
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return selectedStep + 1 === formSteps.length - 1;
};
exports.useIsNextFormStepReview = useIsNextFormStepReview;
var useFormStepNavigation = function (formSteps) {
    var canGoBackStep = useCanGoBackStep(formSteps);
    var canGoForwardStep = useCanGoForwardStep(formSteps);
    var goBackToPreviousStep = useGoBackToPreviousStep(formSteps);
    var goToNextStep = useContinueToNextStep(formSteps);
    var isLastStepSelected = useIsLastStepSelected(formSteps);
    var isNextFormStepReview = (0, exports.useIsNextFormStepReview)(formSteps);
    return {
        canGoBackStep: canGoBackStep,
        canGoForwardStep: canGoForwardStep,
        goBackToPreviousStep: goBackToPreviousStep,
        goToNextStep: goToNextStep,
        isLastStepSelected: isLastStepSelected,
        isNextFormStepReview: isNextFormStepReview,
    };
};
exports.useFormStepNavigation = useFormStepNavigation;
var isFormStepClickable = function (formSteps, index) {
    return index === 0 || formSteps.slice(0, index).every(function (step) { return step.formValid; });
};
exports.isFormStepClickable = isFormStepClickable;
