"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormStepContentWrapper = FormStepContentWrapper;
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var qaIdUtils_1 = require("../../utils/qaIdUtils");
var FormStepContentWrapper_module_scss_1 = require("./FormStepContentWrapper.module.scss");
function FormStepContentWrapper(_a) {
    var header = _a.header, cta = _a.cta, index = _a.index, content = _a.content, qaId = _a.qaId, label = _a.label;
    var qaIdHeader = (0, qaIdUtils_1.kebabLowercaseQaId)(header);
    return (<div className={FormStepContentWrapper_module_scss_1.default.formStepContent} data-qa-id={qaId ? "".concat(qaId, "-").concat(index) : undefined}>
      <div className={FormStepContentWrapper_module_scss_1.default.formStepHeader}>
        <div className={FormStepContentWrapper_module_scss_1.default.formStepHeadlineContainer}>
          <uniform_web_1.Title as="h1" size="xxlarge" className={FormStepContentWrapper_module_scss_1.default.formStepHeadline} qaId={qaId ? "".concat(qaId, "-headline-").concat(qaIdHeader) : undefined}>
            {header}
          </uniform_web_1.Title>
          {label && label}
        </div>
        {cta && <div className={FormStepContentWrapper_module_scss_1.default.cta}>{cta}</div>}
      </div>
      {content}
    </div>);
}
exports.default = FormStepContentWrapper;
