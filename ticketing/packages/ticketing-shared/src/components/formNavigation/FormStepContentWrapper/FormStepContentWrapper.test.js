"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var renderHelpers_1 = require("../../../test/renderHelpers");
var FormStepContentWrapper_1 = require("./FormStepContentWrapper");
describe('FormStepContentWrapper', function () {
    it('Should render the correct step content', function () {
        var container = (0, renderHelpers_1.renderWithOptions)(<FormStepContentWrapper_1.default header="Header" index={1} content={<div data-qa-id="form-wrapper-content">content</div>} qaId={'form-step-content-wrapper'} cta={<div data-qa-id="form-wrapper-content-cta">cta</div>}/>, {
            withIntlProvider: true,
        });
        expect(container.queryByText('content')).toBeInTheDocument();
        expect(container.queryByTestId('form-step-content-wrapper-1')).toBeInTheDocument();
        expect(container.queryByTestId('form-wrapper-content-cta')).toBeInTheDocument();
        expect(container.queryByText('Header')).toBeInTheDocument();
        expect(container.queryByTestId('form-step-content-wrapper-headline-header')).toBeInTheDocument();
    });
});
