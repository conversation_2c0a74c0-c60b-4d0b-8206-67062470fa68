"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var renderHelpers_1 = require("../../../test/renderHelpers");
var FormStepSwitcher_1 = require("./FormStepSwitcher");
var formSteps = [
    {
        formStepIndicatorTitle: 'First Step',
        formValid: true,
    },
    {
        formStepIndicatorTitle: 'Second Step',
        formValid: false,
    },
    {
        formStepIndicatorTitle: 'Third Step',
        formValid: false,
    },
];
describe('FormStepSwitcher', function () {
    it('First step is valid and currently selected, other steps rendered correctly', function () {
        var container = (0, renderHelpers_1.renderWithOptions)(<FormStepSwitcher_1.FormStepSwitcher formSteps={formSteps} qaId={'form-step-switcher'}/>, {
            withIntlProvider: true,
        });
        expect(container.queryByTestId('form-step-switcher-step-wrapper-first-step')).toBeInTheDocument();
        expect(container.queryByTestId('form-step-switcher-step-indicator-first-step-checked-selected')).toBeInTheDocument();
        expect(container.queryByTestId('form-step-switcher-step-wrapper-second-step')).toBeInTheDocument();
        expect(container.queryByTestId('form-step-switcher-step-divider-third-step')).not.toBeInTheDocument();
    });
});
