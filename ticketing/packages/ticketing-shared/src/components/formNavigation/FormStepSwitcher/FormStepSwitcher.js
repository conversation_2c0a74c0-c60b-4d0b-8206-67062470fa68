"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormStepSwitcher = FormStepSwitcher;
var react_1 = require("react");
var client_1 = require("@apollo/client");
var qaIdUtils_1 = require("../../utils/qaIdUtils");
var stateVars_1 = require("../../utils/stateVars");
var FormStepIndicator_1 = require("../FormStepIndicator/FormStepIndicator");
var useFormStepNavigation_1 = require("../hooks/useFormStepNavigation");
var FormStepSwitcher_module_scss_1 = require("./FormStepSwitcher.module.scss");
function FormStepSwitcher(_a) {
    var formSteps = _a.formSteps, qaId = _a.qaId;
    var selectedStep = (0, client_1.useReactiveVar)(stateVars_1.selectedFormStep);
    return (<div className={FormStepSwitcher_module_scss_1.default.formStepSwitcherWrapper} data-qa-id={qaId}>
      {formSteps.map(function (formStep, index) {
            var isLast = index + 1 === formSteps.length;
            var formStepClickable = (0, useFormStepNavigation_1.isFormStepClickable)(formSteps, index);
            var qaIdTitle = (0, qaIdUtils_1.kebabLowercaseQaId)(formStep.formStepIndicatorTitle);
            return (<div key={formStep.formStepIndicatorTitle} className={isLast ? '' : FormStepSwitcher_module_scss_1.default.formStep} data-qa-id={qaId ? "".concat(qaId, "-step-wrapper-").concat(qaIdTitle) : undefined}>
            <FormStepIndicator_1.FormStepIndicator title={formStep.formStepIndicatorTitle} index={index} isLast={isLast} formValid={formStep.formValid} isSelected={selectedStep === index} onSelected={function () { return (0, stateVars_1.selectedFormStep)(index); }} isFormStepClickable={formStepClickable} qaId={qaId}/>
          </div>);
        })}
    </div>);
}
exports.default = FormStepSwitcher;
