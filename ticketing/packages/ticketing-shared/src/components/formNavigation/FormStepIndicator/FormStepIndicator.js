"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FormStepIndicator = FormStepIndicator;
var react_1 = require("react");
var client_1 = require("@apollo/client");
var uniform_web_1 = require("@hudl/uniform-web");
var qaIdUtils_1 = require("../../utils/qaIdUtils");
var stateVars_1 = require("../../utils/stateVars");
var FormStepIndicator_module_scss_1 = require("./FormStepIndicator.module.scss");
function FormStepIndicator(_a) {
    var title = _a.title, onSelected = _a.onSelected, isLast = _a.isLast, index = _a.index, formValid = _a.formValid, isSelected = _a.isSelected, isFormStepClickable = _a.isFormStepClickable, qaId = _a.qaId;
    var isMobile = (0, client_1.useReactiveVar)(stateVars_1.isMobileScreen);
    var qaIdTitle = (0, qaIdUtils_1.kebabLowercaseQaId)(title);
    var indexTitle = index + 1;
    return (<div className={FormStepIndicator_module_scss_1.default.progressStep} key={"formStep-".concat(index)}>
      <div className={isFormStepClickable ? FormStepIndicator_module_scss_1.default.formStepSelected : FormStepIndicator_module_scss_1.default.disabledFormStepSelected} onClick={onSelected} onKeyDown={function () { return onSelected; }} role="button" tabIndex={0} data-qa-id={qaId
            ? "".concat(qaId, "-step-indicator-").concat(qaIdTitle).concat(formValid ? '-checked' : '').concat(isSelected ? '-selected' : '')
            : undefined}>
        <uniform_web_1.SelectMark selectedState={formValid ? 'selected' : 'unselected'} isHovered={isSelected} onClick={onSelected} className={isFormStepClickable ? '' : FormStepIndicator_module_scss_1.default.disabledSelectMark}/>
        <uniform_web_1.Text className={isSelected ? FormStepIndicator_module_scss_1.default.formStepTitleSelected : FormStepIndicator_module_scss_1.default.formStepTitle} qaId={qaId ? "".concat(qaId, "-step-title-").concat(qaIdTitle) : undefined}>
          {isMobile ? indexTitle : title}
        </uniform_web_1.Text>
      </div>
      {!isLast && (<div className={FormStepIndicator_module_scss_1.default.stepDivider}>
          <uniform_web_1.Divider qaId={qaId ? "".concat(qaId, "-step-divider-").concat(qaIdTitle) : undefined}/>
        </div>)}
    </div>);
}
exports.default = FormStepIndicator;
