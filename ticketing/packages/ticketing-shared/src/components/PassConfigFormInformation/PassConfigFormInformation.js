"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassConfigFormInformation = PassConfigFormInformation;
/* eslint-disable max-len -- long inputs */
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var DescriptionInput_1 = require("../../components/DescriptionInput/DescriptionInput");
var PassConfigFormStartEndDateInput_1 = require("../../components/PassConfigFormStartEndDateInput/PassConfigFormStartEndDateInput");
var sharedEnums_1 = require("../../enums/sharedEnums");
var constants_1 = require("../../types/constants");
var validationUtils_1 = require("../../utility/validationUtils");
var PassConfigFormInformation_module_scss_1 = require("./PassConfigFormInformation.module.scss");
function PassConfigFormInformation(props) {
    var passConfigName = props.passConfigName, passConfigDescription = props.passConfigDescription, setPassConfigName = props.setPassConfigName, setPassConfigDescription = props.setPassConfigDescription, setPassConfigFormDetailsError = props.setPassConfigFormDetailsError, setIsPassConfigModified = props.setIsPassConfigModified, startDate = props.startDate, endDate = props.endDate, setStartDate = props.setStartDate, setEndDate = props.setEndDate, setStartEndDateInputFormError = props.setStartEndDateInputFormError;
    var passConfigNameLimitError = (0, validationUtils_1.validateAgainstMaxLength)(passConfigName, constants_1.formTitleMaxLength);
    var passConfigDescriptionLimitError = (0, validationUtils_1.validateAgainstMaxLength)(passConfigDescription, constants_1.formDescriptionMaxLength);
    var _a = react_1.default.useState(false), passNameError = _a[0], setPassNameError = _a[1];
    (0, react_1.useEffect)(function () {
        setPassConfigFormDetailsError(passConfigDescriptionLimitError || passConfigNameLimitError || passNameError);
    }, [passConfigDescriptionLimitError, passConfigNameLimitError, passNameError, setPassConfigFormDetailsError]);
    var onPassNameBlur = react_1.default.useCallback(function (event) {
        var passNameValue = event.currentTarget.value;
        setPassNameError(passNameValue.trim() === '');
    }, [setPassNameError]);
    var onPassNameChange = react_1.default.useCallback(function (event) {
        setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
        setPassConfigName(event.target.value);
        if (event.target.value.trim() !== '') {
            setPassNameError(false);
        }
    }, [setIsPassConfigModified, setPassConfigName]);
    var onPassDescriptionChange = react_1.default.useCallback(function (newDescription) {
        setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
        setPassConfigDescription(newDescription);
    }, [setIsPassConfigModified, setPassConfigDescription]);
    var onPassStartDateChange = react_1.default.useCallback(function (date) {
        setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
        setStartDate(date);
    }, [setIsPassConfigModified, setStartDate]);
    var onPassEndDateChange = react_1.default.useCallback(function (date) {
        setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
        setEndDate(date);
    }, [setIsPassConfigModified, setEndDate]);
    return (<div data-qa-id="pass-config-information-component">
      <uniform_web_1.Text>{(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-information' })}</uniform_web_1.Text>
      <div className={PassConfigFormInformation_module_scss_1.default.passNameDateRange}>
        <uniform_web_forms_legacy_1.Input className={PassConfigFormInformation_module_scss_1.default.nameInput} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-name' })} isRequired value={passConfigName} hasError={passNameError || passConfigNameLimitError} helpText={passNameError
            ? (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-name-error-help-text' })
            : (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-name-character-limit' }, { nameCharactersUsed: passConfigName.length, formTitleMaxLength: constants_1.formTitleMaxLength })} onChange={onPassNameChange} onBlur={onPassNameBlur} placeholder={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-name-instruction' })} qaId={"pass-name-input".concat(passConfigNameLimitError || passNameError ? '-error' : '')}/>
        <PassConfigFormStartEndDateInput_1.default startDate={startDate} endDate={endDate} setEndDate={onPassEndDateChange} setStartDate={onPassStartDateChange} setStartEndDateInputFormError={setStartEndDateInputFormError}/>
      </div>
      <div className={PassConfigFormInformation_module_scss_1.default.passDescription}>
        <DescriptionInput_1.DescriptionInput description={passConfigDescription} setDescription={onPassDescriptionChange} entityType={sharedEnums_1.TicketingEntityType.Pass}/>
      </div>
    </div>);
}
