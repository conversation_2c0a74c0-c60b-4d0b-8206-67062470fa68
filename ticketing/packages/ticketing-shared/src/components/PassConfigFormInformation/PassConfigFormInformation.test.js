"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../test/renderHelpers");
var PassConfigFormInformation_1 = require("./PassConfigFormInformation");
describe('PassConfigFormInformation Tests', function () {
    it('Renders PassConfigFormInformation', function () {
        (0, renderHelpers_1.renderWithOptions)(<PassConfigFormInformation_1.PassConfigFormInformation passConfigName="testing" passConfigDescription="" setPassConfigName={vi.fn()} setPassConfigDescription={vi.fn()} setPassConfigFormDetailsError={vi.fn()} setIsPassConfigModified={vi.fn()} startDate="2023-10-01" endDate="2023-10-31" setStartDate={vi.fn()} setEndDate={vi.fn()} setStartEndDateInputFormError={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('pass-config-information-component')).toBeInTheDocument();
    });
});
