"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../test/renderHelpers");
var PassConfigFormStartEndDateInput_1 = require("./PassConfigFormStartEndDateInput");
describe('PassConfigFormStartEndDateInput Tests', function () {
    it('Renders PassConfigFormStartEndDateInput', function () {
        (0, renderHelpers_1.renderWithOptions)(<PassConfigFormStartEndDateInput_1.default setIsPassConfigModified={vi.fn()} startDate="2025-05-07" endDate="2025-06-09" setStartDate={vi.fn()} setEndDate={vi.fn()} setStartEndDateInputFormError={vi.fn()}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByTestId('pass-start-date-input')).toBeInTheDocument();
        expect(react_1.screen.getByTestId('pass-end-date-input')).toBeInTheDocument();
    });
});
