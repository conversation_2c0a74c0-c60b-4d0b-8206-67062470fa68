"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var uniform_web_forms_legacy_1 = require("@hudl/uniform-web-forms-legacy");
var frontends_i18n_1 = require("frontends-i18n");
var PassConfigFormStartEndDateInput_module_scss_1 = require("./PassConfigFormStartEndDateInput.module.scss");
function PassConfigFormStartEndDateInput(props) {
    var setIsPassConfigModified = props.setIsPassConfigModified, startDate = props.startDate, endDate = props.endDate, setStartDate = props.setStartDate, setEndDate = props.setEndDate, setStartEndDateInputFormError = props.setStartEndDateInputFormError;
    var passEndDateError = !!startDate && !!endDate && endDate < startDate;
    (0, react_1.useEffect)(function () {
        setStartEndDateInputFormError(passEndDateError || !startDate || !endDate);
    }, [endDate, passEndDateError, setStartEndDateInputFormError, startDate]);
    return (<div className={PassConfigFormStartEndDateInput_module_scss_1.default.dateRangeContainer}>
      <div>
        <uniform_web_forms_legacy_1.Input className={PassConfigFormStartEndDateInput_module_scss_1.default.dateInput} type="date" label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.start-date-label' })} value={startDate} onChange={function (e) {
            setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
            setStartDate(e.target.value);
        }} max={endDate} isRequired qaId="pass-start-date-input"/>
      </div>
      <div>
        <uniform_web_forms_legacy_1.Input className={PassConfigFormStartEndDateInput_module_scss_1.default.dateInput} type="date" label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.end-date-label' })} value={endDate} onChange={function (e) {
            setIsPassConfigModified === null || setIsPassConfigModified === void 0 ? void 0 : setIsPassConfigModified(true);
            setEndDate(e.target.value);
        }} min={startDate} hasError={passEndDateError} helpText={passEndDateError ? (0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.end-date-error' }) : ''} isRequired qaId="pass-end-date-input"/>
      </div>
    </div>);
}
exports.default = PassConfigFormStartEndDateInput;
