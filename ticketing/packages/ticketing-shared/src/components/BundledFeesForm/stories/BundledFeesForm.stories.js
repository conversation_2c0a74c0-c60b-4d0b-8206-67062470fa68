"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Demo = void 0;
var uniform_web_1 = require("@hudl/uniform-web");
var sharedEnums_1 = require("../../../enums/sharedEnums");
var BundledFeesForm_1 = require("../BundledFeesForm");
// Adjust the import according to your tooltip library
exports.default = {
    title: 'General/BundledFeesForm',
    component: BundledFeesForm_1.BundledFeesForm,
};
var feeStrategyOptions = [
    {
        feeStrategy: sharedEnums_1.FeeStrategy.PaidByCustomer,
        label: 'Your Fans',
        helperText: 'Your fans cover the fees.',
        exampleText: 'This is example text.',
    },
    {
        feeStrategy: sharedEnums_1.FeeStrategy.PaidByOrganization,
        label: 'Your Organization',
        helperText: 'You cover the fees.',
        exampleText: 'This is example text.',
    },
];
exports.Demo = {
    args: {
        feeStrategyOptions: feeStrategyOptions,
        feeStrategy: {},
        onFeeStrategyChange: function (e) {
            console.log('Fee strategy changed', e.target.value);
        },
    },
    render: function (args) { return (<uniform_web_1.TooltipProvider>
      <BundledFeesForm_1.BundledFeesForm {...args}/>
    </uniform_web_1.TooltipProvider>); },
};
