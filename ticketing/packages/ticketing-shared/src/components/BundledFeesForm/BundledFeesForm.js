"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundledFeesForm = BundledFeesForm;
var react_1 = require("react");
var uniform_web_1 = require("@hudl/uniform-web");
var frontends_i18n_1 = require("frontends-i18n");
var BundledFeesForm_module_scss_1 = require("./BundledFeesForm.module.scss");
function BundledFeesForm(props) {
    var feeStrategy = props.feeStrategy, feeStrategyOptions = props.feeStrategyOptions, onFeeStrategyChange = props.onFeeStrategyChange, errorRecalculationMethod = props.errorRecalculationMethod;
    var _a = (0, react_1.useState)(null), openTooltipIndex = _a[0], setOpenTooltipIndex = _a[1];
    (0, react_1.useEffect)(function () {
        if (errorRecalculationMethod)
            errorRecalculationMethod();
        // eslint-disable-next-line react-hooks/exhaustive-deps -- prevents infinite loop
    }, [feeStrategy]);
    return (<div className={BundledFeesForm_module_scss_1.default.bundledFeesContainer} data-qa-id="bundled-fees-container">
      <uniform_web_1.Text className={BundledFeesForm_module_scss_1.default.headerText}>
        {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.fee-strategy.who-covers-fees' }, {
            feesForEventLink: (<uniform_web_1.Link type="article" href="http://support.hudl.com/s/article/set-up-bundled-fees" target="_blank" qaId="bundled-fees-tutorial-link">
                {(0, frontends_i18n_1.formatMessage)({
                    id: 'ticketing-shared.fee-strategy.who-covers-fees-link-purchases',
                })}
              </uniform_web_1.Link>),
        })}
      </uniform_web_1.Text>
      {feeStrategyOptions.map(function (option, index) {
            var qaId = option.label.toLowerCase().replace(/\s+/g, '-');
            var isChecked = feeStrategy === option.feeStrategy;
            return (<div className={BundledFeesForm_module_scss_1.default.radioContainer} key={option.feeStrategy}>
            <div className={BundledFeesForm_module_scss_1.default.tooltipContent}>
              <uniform_web_1.Tooltip content={option.helperText} isOpen={openTooltipIndex === index} qaId={"bundled-fees-tooltip-".concat(qaId)} position="right">
                <div className={BundledFeesForm_module_scss_1.default.radioContent}>
                  <uniform_web_1.Radio key={option.feeStrategy} value={option.feeStrategy} label={option.label} onChange={onFeeStrategyChange} isChecked={isChecked} qaId={"bundled-fees-radio-".concat(qaId).concat(isChecked ? '-checked' : '')}/>
                  <span onMouseEnter={function () { return setOpenTooltipIndex(index); }} onMouseLeave={function () { return setOpenTooltipIndex(null); }} data-qa-id={"tooltip-icon-".concat(qaId)}>
                    <uniform_web_1.IconInformation size="small"/>
                  </span>
                </div>
              </uniform_web_1.Tooltip>
            </div>
            <div className={BundledFeesForm_module_scss_1.default.exampleText}>{option.exampleText}</div>
          </div>);
        })}
    </div>);
}
