"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_2 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var sharedEnums_1 = require("../../enums/sharedEnums");
var renderHelpers_1 = require("../../test/renderHelpers");
var BundledFeesForm_1 = require("./BundledFeesForm");
var mockOnChange = vi.fn();
var mockRecalculate = vi.fn();
var feeStrategyOptions = [
    {
        feeStrategy: sharedEnums_1.FeeStrategy.PaidByCustomer,
        label: 'Your Fans',
        helperText: 'Fees will automatically be applied for fans at checkout.',
        exampleText: 'Ex. $10 ticket, fans would pay $11.64 and you would receive $10',
    },
    {
        feeStrategy: sharedEnums_1.FeeStrategy.PaidByOrganization,
        label: 'Your Organization',
        helperText: "You don't pay any extra - fees will automatically be deducted from your total revenue.",
        exampleText: 'Ex. $10 ticket, fans would pay $10 and you would receive $8.41',
    },
];
describe('BundledFeesForm', function () {
    beforeEach(function () {
        mockOnChange.mockClear();
        mockRecalculate.mockClear();
    });
    it('should render bundled fees form with all options', function () {
        (0, renderHelpers_1.renderWithOptions)(<BundledFeesForm_1.BundledFeesForm feeStrategyOptions={feeStrategyOptions} feeStrategy={sharedEnums_1.FeeStrategy.PaidByCustomer} onFeeStrategyChange={mockOnChange} errorRecalculationMethod={mockRecalculate}/>, {
            withIntlProvider: true,
        });
        expect(react_2.screen.getByTestId('bundled-fees-container')).toBeInTheDocument();
        // Verify both radio options are present
        expect(react_2.screen.getByTestId('bundled-fees-radio-your-fans-checked')).toBeInTheDocument();
        expect(react_2.screen.getByTestId('bundled-fees-radio-your-organization')).toBeInTheDocument();
        // Verify example texts
        feeStrategyOptions.forEach(function (option) {
            expect(react_2.screen.getByText(option.exampleText)).toBeInTheDocument();
        });
    });
    it('calls onChange and recalculates when fee strategy changes', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<BundledFeesForm_1.BundledFeesForm feeStrategyOptions={feeStrategyOptions} feeStrategy={sharedEnums_1.FeeStrategy.PaidByCustomer} onFeeStrategyChange={mockOnChange} errorRecalculationMethod={mockRecalculate}/>, {
                        withIntlProvider: true,
                    });
                    user = user_event_1.default.setup();
                    return [4 /*yield*/, user.click(react_2.screen.getByTestId('bundled-fees-radio-your-organization'))];
                case 1:
                    _a.sent();
                    expect(mockOnChange).toHaveBeenCalledTimes(1);
                    // Initial mount + change = 2 calls
                    expect(mockRecalculate).toHaveBeenCalledTimes(1);
                    return [2 /*return*/];
            }
        });
    }); });
});
