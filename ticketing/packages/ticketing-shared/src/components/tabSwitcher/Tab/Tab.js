"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Tab = Tab;
var uniform_web_1 = require("@hudl/uniform-web");
var Tab_module_scss_1 = require("./Tab.module.scss");
function Tab(_a) {
    var title = _a.title, index = _a.index, dataQaId = _a.dataQaId, isSelected = _a.isSelected, _b = _a.onSelected, onSelected = _b === void 0 ? function () { } : _b;
    return (<button value={index} className={Tab_module_scss_1.default.tabButton} type="button" onClick={onSelected} data-qa-id={"".concat(dataQaId, "-").concat(title.split(' ').join('-').toLowerCase(), "-tab").concat(isSelected ? '-selected' : '')}>
      <uniform_web_1.Text className={isSelected ? Tab_module_scss_1.default.tabTitleSelected : Tab_module_scss_1.default.tabTitle}>{title} </uniform_web_1.Text>
    </button>);
}
