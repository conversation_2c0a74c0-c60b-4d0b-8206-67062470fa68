"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TabSwitcher = TabSwitcher;
var Tab_1 = require("../Tab/Tab");
var TabSwitcher_module_scss_1 = require("./TabSwitcher.module.scss");
function TabSwitcher(_a) {
    var tabs = _a.tabs, dataQaId = _a.dataQaId, selectedTab = _a.selectedTab, setSelectedTabReactiveVar = _a.setSelectedTabReactiveVar;
    return (<ul className={TabSwitcher_module_scss_1.default.tabsSwitcher}>
      <div className={TabSwitcher_module_scss_1.default.tabsContainer}>
        {tabs.map(function (tab) { return (<li key={tab.index} className={tab.index === selectedTab ? TabSwitcher_module_scss_1.default.tabTitleSelected : TabSwitcher_module_scss_1.default.tabTitle}>
            <Tab_1.Tab title={tab.title} index={tab.index} isSelected={tab.isSelected} onSelected={function () {
                setSelectedTabReactiveVar(tab.index);
            }} dataQaId={dataQaId}/>
          </li>); })}
      </div>
    </ul>);
}
