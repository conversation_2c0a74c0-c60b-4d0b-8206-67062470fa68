"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
/* eslint-disable max-len -- long imports */
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var sharedEnums_1 = require("../../enums/sharedEnums");
var renderHelpers_1 = require("../../test/renderHelpers");
var testHelpers_1 = require("../../test/testHelpers");
var PassConfigFormPricingInput_1 = require("./PassConfigFormPricingInput");
describe('PassConfigFormPricingInput Tests', function () {
    it('Renders PassConfigFormPricingInput', function () {
        (0, renderHelpers_1.renderWithOptions)(<PassConfigFormPricingInput_1.PassConfigFormPricingInput setFormModified={vi.fn()} freeTicketingOnly={false} passConfigPassPrice={100} feeStrategy={sharedEnums_1.FeeStrategy.PaidByOrganization} setPassConfigPassPrice={vi.fn()} setPassConfigPriceError={vi.fn()} passConfigPriceError={false}/>, {
            withIntlProvider: true,
        });
        expect(react_1.screen.getByText('Pass Pricing')).toBeInTheDocument();
    });
    it('Error message is shown when pass price lower than paid by org fee threshold', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, mockSetPassConfigPriceError, mockOnPassPriceChange;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    mockSetPassConfigPriceError = vi.fn();
                    mockOnPassPriceChange = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<PassConfigFormPricingInput_1.PassConfigFormPricingInput setFormModified={vi.fn()} freeTicketingOnly={false} passConfigPassPrice={0} feeStrategy={sharedEnums_1.FeeStrategy.PaidByOrganization} setPassConfigPassPrice={mockOnPassPriceChange} setPassConfigPriceError={mockSetPassConfigPriceError} passConfigPriceError={false}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByText('Pass Pricing')).toBeInTheDocument();
                    return [4 /*yield*/, (0, testHelpers_1.clearPriceInput)(react_1.screen.getByTestId('price-input'), user)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.paste('3.00')];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, (0, react_1.waitFor)(function () { return expect(mockOnPassPriceChange).toHaveBeenCalledWith(300); })];
                case 3:
                    _a.sent();
                    expect(mockSetPassConfigPriceError).toHaveBeenCalledWith(true);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Error message is not shown when pass price lower than paid by fan fee threshold', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, mockSetPassConfigPriceError;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    mockSetPassConfigPriceError = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<PassConfigFormPricingInput_1.PassConfigFormPricingInput setFormModified={vi.fn()} freeTicketingOnly={false} passConfigPassPrice={300} feeStrategy={sharedEnums_1.FeeStrategy.PaidByCustomer} setPassConfigPassPrice={vi.fn()} setPassConfigPriceError={mockSetPassConfigPriceError} passConfigPriceError={false}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByText('Pass Pricing')).toBeInTheDocument();
                    return [4 /*yield*/, (0, testHelpers_1.clearPriceInput)(react_1.screen.getByTestId('price-input'), user)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.paste('3.00')];
                case 2:
                    _a.sent();
                    expect(mockSetPassConfigPriceError).toHaveBeenCalledWith(false);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Error message not shown when pass price is $0 with paid by org fee', function () { return __awaiter(void 0, void 0, void 0, function () {
        var user, mockSetPassConfigPriceError;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    user = user_event_1.default.setup({ delay: null });
                    mockSetPassConfigPriceError = vi.fn();
                    (0, renderHelpers_1.renderWithOptions)(<PassConfigFormPricingInput_1.PassConfigFormPricingInput setFormModified={vi.fn()} freeTicketingOnly={false} passConfigPassPrice={100} feeStrategy={sharedEnums_1.FeeStrategy.PaidByOrganization} setPassConfigPassPrice={vi.fn()} setPassConfigPriceError={mockSetPassConfigPriceError} passConfigPriceError={false}/>, {
                        withIntlProvider: true,
                    });
                    expect(react_1.screen.getByText('Pass Pricing')).toBeInTheDocument();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('price-input'))];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.paste('0.00')];
                case 2:
                    _a.sent();
                    expect(mockSetPassConfigPriceError).toHaveBeenCalledWith(false);
                    return [2 /*return*/];
            }
        });
    }); });
});
