"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PassConfigFormPricingInput = PassConfigFormPricingInput;
var uniform_web_1 = require("@hudl/uniform-web");
var frontends_i18n_1 = require("frontends-i18n");
var sharedEnums_1 = require("../../enums/sharedEnums");
var constants_1 = require("../../types/constants");
var PriceInput_1 = require("../PriceInput/PriceInput");
var PassConfigFormPricingInput_module_scss_1 = require("./PassConfigFormPricingInput.module.scss");
function PassConfigFormPricingInput(props) {
    var setFormModified = props.setFormModified, freeTicketingOnly = props.freeTicketingOnly, passConfigPassPrice = props.passConfigPassPrice, setPassConfigPassPrice = props.setPassConfigPassPrice, setPassConfigPriceError = props.setPassConfigPriceError, passConfigPriceError = props.passConfigPriceError, feeStrategy = props.feeStrategy;
    var onPassPriceChange = function (price) {
        setFormModified === null || setFormModified === void 0 ? void 0 : setFormModified(true);
        setPassConfigPassPrice(price);
        setPassConfigPriceError(price < constants_1.minimumPassPriceForBundledFeesInCents && price !== 0 && feeStrategy === sharedEnums_1.FeeStrategy.PaidByOrganization);
    };
    return (<div>
      <uniform_web_1.Subhead>{(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-price-details-label' })}</uniform_web_1.Subhead>
      {!freeTicketingOnly && (<uniform_web_1.Text className={PassConfigFormPricingInput_module_scss_1.default.description}>
          {(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.create-pass-page.pass-price-details-description' })}
        </uniform_web_1.Text>)}
      <div className={PassConfigFormPricingInput_module_scss_1.default.passPriceInputContainer}>
        <PriceInput_1.PriceInput currency={sharedEnums_1.Currency.USD} priceInCents={passConfigPassPrice} onPriceChange={onPassPriceChange} isDisabled={freeTicketingOnly} hasError={passConfigPriceError} label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.price' })} errorMessage={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.fee-strategy.pass-price-minimum' })}/>
      </div>
    </div>);
}
