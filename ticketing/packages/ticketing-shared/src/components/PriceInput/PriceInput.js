"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceInput = PriceInput;
var react_1 = require("react");
var classnames_1 = require("classnames");
var uniform_web_1 = require("@hudl/uniform-web");
var priceInputConfigUtility_1 = require("../../utility/priceInputConfigUtility");
var PriceInput_module_scss_1 = require("./PriceInput.module.scss");
var DIGIT_REGEX = /^[0-9]$/;
var NON_DIGIT_REGEX = /[^0-9]/g;
var KEY_BACKSPACE = 'Backspace';
var KEY_DELETE = 'Delete';
var KEY_ENTER = 'Enter';
var KEY_COMMA = ',';
var KEY_PERIOD = '.';
var KEY_SPACE = ' ';
var KEYS_NOT_ALLOWED = /^[a-zA-Z !"#$%&'()*+\-/:;<=>?@[\\\]^_`{|}~]$/;
function digitsToNumber(digits) {
    return digits.reduce(function (acc, digit) { return acc * 10 + digit; }, 0);
}
function numberToDigits(n, decimalPlaces) {
    if (n === 0)
        return { integer: [0], fractional: Array(decimalPlaces).fill(0) };
    // Split the number into a digits array
    var digits = [];
    var temp = n;
    while (temp > 0) {
        digits.unshift(temp % 10);
        temp = Math.floor(temp / 10);
    }
    // Pad with zeros if needed
    while (digits.length < decimalPlaces + 1) {
        digits.unshift(0);
    }
    var integer = digits.slice(0, digits.length - decimalPlaces);
    var fractional = digits.slice(-decimalPlaces);
    return { integer: integer, fractional: fractional };
}
// Helper to map caret position to digit index
function getDigitIndexFromCaretPos(caret, displayValue) {
    var idx = 0;
    for (var i = 0; i < displayValue.length && i < caret; i++) {
        if (displayValue[i].match(/\d/))
            idx++;
    }
    return idx;
}
// Helper to map digit index to caret position in the display string
function getCaretFromDigitIndex(nextDigitIndex, digits, display, moveCaretToDecimalSide, decimalPlaces) {
    if (nextDigitIndex === 0)
        return 0;
    if (nextDigitIndex === digits.length - decimalPlaces && moveCaretToDecimalSide) {
        return display.length - decimalPlaces;
    }
    var digitCount = 0;
    for (var i = 0; i < display.length; i++) {
        if (display[i].match(/\d/)) {
            digitCount++;
            if (digitCount === nextDigitIndex)
                return i + 1;
        }
    }
    return display.length;
}
// Take price number value and format it to a string with the given decimal and thousand separators
function formatFromNumber(price, decimalPlaces, decimalSeparator, thousandSeparator) {
    var str = price.toString().padStart(decimalPlaces + 1, '0');
    var intPart = str.slice(0, -decimalPlaces) || '0';
    var decPart = str.slice(-decimalPlaces).padEnd(decimalPlaces, '0');
    var intStr = intPart.replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator);
    return "".concat(intStr).concat(decimalSeparator).concat(decPart);
}
function PriceInput(props) {
    var _a, _b, _c, _d, _e;
    var currency = props.currency, priceInCents = props.priceInCents, onPriceChange = props.onPriceChange, label = props.label, helpText = props.helpText, _f = props.maximumDigits, maximumDigits = _f === void 0 ? 9 : _f, isRequired = props.isRequired, isDisabled = props.isDisabled, isReadOnly = props.isReadOnly, _g = props.hasError, hasError = _g === void 0 ? false : _g, errorMessage = props.errorMessage, onFocus = props.onFocus, onBlur = props.onBlur, qaId = props.qaId, className = props.className;
    var _h = (0, priceInputConfigUtility_1.getPriceInputConfigForCurrency)(currency), decimalPlaces = _h.decimalPlaces, thousandSeparator = _h.thousandSeparator, decimalSeparator = _h.decimalSeparator, currencySymbol = _h.currencySymbol;
    var inputRef = (0, react_1.useRef)(null);
    var isLocalChange = (0, react_1.useRef)(false);
    var _j = (0, react_1.useState)(priceInCents), internalPriceInCents = _j[0], setInternalPriceInCents = _j[1];
    var _k = (0, react_1.useState)(0), cursor = _k[0], setCursor = _k[1];
    var _l = (0, react_1.useState)(false), shouldRerender = _l[0], setShouldRerender = _l[1];
    var _m = (0, react_1.useState)(0), updateCounter = _m[0], setUpdateCounter = _m[1];
    (0, react_1.useEffect)(function () {
        if (!isLocalChange.current && priceInCents !== internalPriceInCents) {
            setInternalPriceInCents(priceInCents);
        }
        isLocalChange.current = false;
        // eslint-disable-next-line react-hooks/exhaustive-deps -- only run when external priceInCents changes
    }, [priceInCents]);
    var handlePriceChange = (0, react_1.useCallback)(function (newValue) {
        if (newValue !== internalPriceInCents) {
            setInternalPriceInCents(newValue);
            isLocalChange.current = true;
            onPriceChange(newValue);
        }
    }, [internalPriceInCents, onPriceChange]);
    var processKeyPress = (0, react_1.useCallback)(function (inputtedKey, digitIndex, isOnIntegerSideOfDecimal, isFullInputSelected) {
        var _a = numberToDigits(internalPriceInCents, decimalPlaces), integer = _a.integer, fractional = _a.fractional;
        // Handle digit input
        if (DIGIT_REGEX.test(inputtedKey)) {
            var digit = parseInt(inputtedKey, 10);
            // Replace all digits with the new digit and fill 0s for decimal places
            if (isFullInputSelected) {
                return {
                    newInteger: [digit],
                    newFractional: Array(decimalPlaces).fill(0),
                    nextDigitIndex: 1,
                    moveCaretToDecimalSide: false,
                };
            }
            // If all digits are zero, and cursor is at end, start typing from beginning
            var isCaretAtEnd = digitIndex === integer.length + fractional.length;
            if (isCaretAtEnd && internalPriceInCents === 0) {
                return {
                    newInteger: [digit],
                    newFractional: fractional,
                    nextDigitIndex: integer.length,
                    moveCaretToDecimalSide: false,
                };
            }
            // Don't allow adding more decimals than the configured decimal places
            if (digitIndex >= integer.length + fractional.length) {
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: digitIndex,
                    moveCaretToDecimalSide: false,
                };
            }
            // Insert digit at the correct index
            // Handle integer side of the decimal
            if (digitIndex <= integer.length && isOnIntegerSideOfDecimal) {
                // If the first digit is 0, replace it with the new digit
                if (integer.length === 1 && integer[0] === 0) {
                    return { newInteger: [digit], newFractional: fractional, nextDigitIndex: 1, moveCaretToDecimalSide: false };
                }
                // Otherwise, insert the digit at the correct index
                integer.splice(digitIndex, 0, digit);
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: digitIndex + 1,
                    moveCaretToDecimalSide: false,
                };
            }
            // Handle fractional - value after caret is replaced with the new digit
            var decimalIndex = digitIndex - integer.length;
            fractional.splice(decimalIndex, 1, digit);
            return {
                newInteger: integer,
                newFractional: fractional,
                nextDigitIndex: digitIndex + 1,
                moveCaretToDecimalSide: false,
            };
        }
        // Handle backspace/delete
        if (inputtedKey === KEY_BACKSPACE || inputtedKey === KEY_DELETE) {
            // Clear all digits
            if (isFullInputSelected) {
                integer = [0];
                fractional = Array(decimalPlaces).fill(0);
                return {
                    newInteger: [0],
                    newFractional: Array(decimalPlaces).fill(0),
                    nextDigitIndex: 0,
                    moveCaretToDecimalSide: false,
                };
            }
            // Do nothing if deleting from the start
            if (digitIndex === 0) {
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: digitIndex,
                    moveCaretToDecimalSide: false,
                };
            }
            // Deleting digit from integer - remove digit at the correct index
            if (digitIndex <= integer.length) {
                if (integer.length === 0) {
                    // If deleting the first integer digit, set it to 0
                    return {
                        newInteger: [0],
                        newFractional: fractional,
                        nextDigitIndex: 0,
                        moveCaretToDecimalSide: false,
                    };
                }
                // If deleting any other digit, shift all digits to the left
                integer.splice(digitIndex - 1, 1);
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: digitIndex - 1,
                    moveCaretToDecimalSide: false,
                };
            }
            // Deleting digit from fractional - remove digit at the correct index
            var decimalIndex = digitIndex - integer.length;
            // If deleting the last fractional digit, set it to 0
            if (decimalIndex === fractional.length) {
                fractional.splice(decimalIndex - 1, 1, 0);
                setUpdateCounter(function (prev) { return prev + 1; });
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: digitIndex - 1,
                    moveCaretToDecimalSide: true,
                };
            }
            // Shift all fractional digits to the left, filling the last with 0
            if (decimalIndex <= fractional.length) {
                for (var i = decimalIndex - 1; i < fractional.length - 1; i++) {
                    fractional[i] = fractional[i + 1];
                }
                fractional[fractional.length - 1] = 0;
            }
            return {
                newInteger: integer,
                newFractional: fractional,
                nextDigitIndex: digitIndex - 1,
                moveCaretToDecimalSide: true,
            };
        }
        // Handle period/comma/space
        if (inputtedKey === KEY_PERIOD || inputtedKey === KEY_COMMA) {
            // If the caret is on the integer side of the decimal, move it to the fractional side
            if (isOnIntegerSideOfDecimal) {
                setUpdateCounter(function (prev) { return prev + 1; });
                return {
                    newInteger: integer,
                    newFractional: fractional,
                    nextDigitIndex: integer.length,
                    moveCaretToDecimalSide: true,
                };
            }
        }
        // Fallback - noop
        return {
            newInteger: integer,
            newFractional: fractional,
            nextDigitIndex: digitIndex,
            moveCaretToDecimalSide: false,
        };
    }, [decimalPlaces, internalPriceInCents]);
    var onKeyDown = (0, react_1.useCallback)(function (e) {
        var _a, _b;
        if (isDisabled || isReadOnly)
            return;
        var inputtedKey = e.key;
        // Prevent default action for all keys not being used with a modifier
        // (e.g. Ctrl + C, Ctrl + V, etc. will still work)
        if (!e.ctrlKey && !e.metaKey && KEYS_NOT_ALLOWED.test(inputtedKey)) {
            e.preventDefault();
            return;
        }
        // Only allow digits, backspace, delete, period, and comma
        if (!(DIGIT_REGEX.test(inputtedKey) ||
            [KEY_BACKSPACE, KEY_DELETE, KEY_ENTER, KEY_COMMA, KEY_PERIOD].includes(inputtedKey))) {
            return;
        }
        e.preventDefault();
        setShouldRerender(true);
        var inputElement = inputRef.current;
        var displayValue = formatFromNumber(internalPriceInCents, decimalPlaces, decimalSeparator, thousandSeparator);
        var caretIndex = (_b = (_a = inputRef.current) === null || _a === void 0 ? void 0 : _a.selectionStart) !== null && _b !== void 0 ? _b : displayValue.length;
        var digitIndex = getDigitIndexFromCaretPos(caretIndex, displayValue);
        var isOnIntegerSideOfDecimal = caretIndex < displayValue.length - decimalPlaces;
        var isFullInputSelected = inputElement.selectionStart === 0 && inputElement.selectionEnd === inputElement.value.length;
        var _c = processKeyPress(inputtedKey, digitIndex, isOnIntegerSideOfDecimal, isFullInputSelected), newInteger = _c.newInteger, newFractional = _c.newFractional, nextDigitIndex = _c.nextDigitIndex, moveCaretToDecimalSide = _c.moveCaretToDecimalSide;
        var allDigits = __spreadArray(__spreadArray([], newInteger, true), newFractional, true);
        // Prevent adding more digits than the maximum allowed
        if (allDigits.length > maximumDigits) {
            return;
        }
        var newValue = digitsToNumber(allDigits);
        var nextCursor = getCaretFromDigitIndex(nextDigitIndex, allDigits, formatFromNumber(newValue, decimalPlaces, decimalSeparator, thousandSeparator), moveCaretToDecimalSide, decimalPlaces);
        setCursor(nextCursor);
        handlePriceChange(newValue);
    }, [
        decimalPlaces,
        decimalSeparator,
        handlePriceChange,
        internalPriceInCents,
        isDisabled,
        isReadOnly,
        maximumDigits,
        processKeyPress,
        thousandSeparator,
    ]);
    (0, react_1.useEffect)(function () {
        // Prevent cursor jumping if editing value
        if (shouldRerender && inputRef.current && document.activeElement === inputRef.current) {
            inputRef.current.setSelectionRange(cursor, cursor);
        }
    }, [internalPriceInCents, cursor, inputRef, shouldRerender, updateCounter]);
    var handleFullValueInputted = (0, react_1.useCallback)(function (value) {
        var _a, _b;
        var digits = (_a = value === null || value === void 0 ? void 0 : value.replace(NON_DIGIT_REGEX, '')) !== null && _a !== void 0 ? _a : '';
        var asInt = (_b = parseInt(value, 10)) !== null && _b !== void 0 ? _b : 0;
        var intDecimalLength = digits.length;
        if (intDecimalLength > maximumDigits || asInt <= 0) {
            handlePriceChange(0);
            return;
        }
        handlePriceChange(asInt);
    }, [handlePriceChange, maximumDigits]);
    var onPaste = (0, react_1.useCallback)(function (e) {
        e.preventDefault();
        if (isDisabled || isReadOnly)
            return;
        var text = (e.clipboardData.getData('text/plain') ||
            e.clipboardData.getData('Text') ||
            e.clipboardData.getData('text') ||
            '').replace(NON_DIGIT_REGEX, '');
        handleFullValueInputted(text);
    }, [handleFullValueInputted, isDisabled, isReadOnly]);
    var onChange = (0, react_1.useCallback)(function (e) {
        if (isDisabled || isReadOnly)
            return;
        var value = e.target.value;
        handleFullValueInputted(value);
    }, [handleFullValueInputted, isDisabled, isReadOnly]);
    var onClickCurrencySymbol = (0, react_1.useCallback)(function () {
        var inputElement = inputRef.current;
        if (inputElement) {
            inputElement.setSelectionRange(0, 0);
            inputElement.focus();
        }
    }, []);
    var showHelpText = helpText && (!hasError || (hasError && !errorMessage));
    var showErrorMessage = hasError && errorMessage;
    var valueToRender = (0, react_1.useMemo)(function () {
        return formatFromNumber(internalPriceInCents, decimalPlaces, decimalSeparator, thousandSeparator);
    }, [decimalPlaces, decimalSeparator, internalPriceInCents, thousandSeparator]);
    var priceInputQaId = (0, react_1.useMemo)(function () {
        var finalQaId = '';
        if (qaId) {
            finalQaId = "".concat(qaId, "-price-input");
        }
        else {
            finalQaId = 'price-input';
        }
        if (isDisabled) {
            finalQaId += '-disabled';
        }
        if (hasError) {
            finalQaId += '-error';
        }
        if (isReadOnly) {
            finalQaId += '-read-only';
        }
        return finalQaId;
    }, [hasError, isDisabled, isReadOnly, qaId]);
    return (<div className={(0, classnames_1.default)(className, PriceInput_module_scss_1.default.form, (_a = {},
            _a[PriceInput_module_scss_1.default.formError] = hasError,
            _a[PriceInput_module_scss_1.default.formReadOnly] = isReadOnly && !isDisabled,
            _a[PriceInput_module_scss_1.default.formDisabled] = isDisabled,
            _a))}>
      <label htmlFor="price-input" className={(0, classnames_1.default)(PriceInput_module_scss_1.default.formLabel, (_b = {}, _b[PriceInput_module_scss_1.default.formLabelError] = hasError, _b))} data-qa-id={"".concat(priceInputQaId, "-label")}>
        {label}
        {isRequired && <span className={PriceInput_module_scss_1.default.formRequired}/>}
      </label>
      <div className={(0, classnames_1.default)(PriceInput_module_scss_1.default.priceInput, (_c = {},
            _c[PriceInput_module_scss_1.default.priceInputError] = hasError,
            _c[PriceInput_module_scss_1.default.inputReadOnly] = isReadOnly,
            _c))}>
        <button className={(0, classnames_1.default)(PriceInput_module_scss_1.default.currencySymbol, (_d = {},
            _d[PriceInput_module_scss_1.default.currencySymbolError] = hasError,
            _d[PriceInput_module_scss_1.default.currencySymbolReadOnly] = isReadOnly,
            _d))} tabIndex={-1} onClick={onClickCurrencySymbol} onKeyDown={function (e) {
            if (e.key === KEY_ENTER || e.key === KEY_SPACE) {
                e.preventDefault();
                onClickCurrencySymbol();
            }
        }} data-qa-id={"".concat(priceInputQaId, "-currency-symbol")}>
          {currencySymbol}
        </button>
        <input className={(0, classnames_1.default)(PriceInput_module_scss_1.default.input, (_e = {}, _e[PriceInput_module_scss_1.default.formError] = hasError, _e[PriceInput_module_scss_1.default.inputReadOnly] = isReadOnly, _e))} id="price-input" ref={inputRef} type="numeric" inputMode="decimal" value={valueToRender} onKeyDown={onKeyDown} onChange={onChange} onPaste={onPaste} readOnly={isReadOnly} onFocus={onFocus} onBlur={onBlur} data-qa-id={priceInputQaId} disabled={isDisabled}/>
      </div>
      {showHelpText && (<div className={PriceInput_module_scss_1.default.helpText} data-qa-id={"".concat(priceInputQaId, "-help-text")}>
          {helpText}
        </div>)}
      {showErrorMessage && (<div className={PriceInput_module_scss_1.default.errorMessage}>
          <uniform_web_1.IconCritical color="critical" size="small" className={PriceInput_module_scss_1.default.errorMessageIcon}/>
          <p className={PriceInput_module_scss_1.default.errorMessageText} data-qa-id={"".concat(priceInputQaId, "-error-message")}>
            {errorMessage}
          </p>
        </div>)}
    </div>);
}
