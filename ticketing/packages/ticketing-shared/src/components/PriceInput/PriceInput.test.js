"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var sharedEnums_1 = require("../../enums/sharedEnums");
var PriceInput_1 = require("./PriceInput");
var KEY_BACKSPACE = '{backspace}';
describe('PriceInput - Display', function () {
    it('Displays basic information', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD} label="Price" helpText="This is a help text"/>);
        var input = react_1.screen.getByTestId('price-input');
        expect(input).toHaveValue('12.34');
        expect(react_1.screen.getByTestId('price-input-currency-symbol')).toHaveTextContent('$');
        expect(react_1.screen.getByTestId('price-input-label')).toHaveTextContent('Price');
        expect(react_1.screen.getByTestId('price-input-help-text')).toBeInTheDocument();
        expect(react_1.screen.queryByTestId('price-input-error-message')).not.toBeInTheDocument();
        expect(react_1.screen.queryByTestId('price-input-error-text')).not.toBeInTheDocument();
    });
    it('Displays 0.00 when priceInCents is 0', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={0} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD}/>);
        var input = react_1.screen.getByTestId('price-input');
        expect(input).toHaveValue('0.00');
    });
    it('Displays 0.12 when priceInCents is 12', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={12} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD}/>);
        var input = react_1.screen.getByTestId('price-input');
        expect(input).toHaveValue('0.12');
    });
    it('Displays 12.34 when priceInCents is 1234', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD}/>);
        var input = react_1.screen.getByTestId('price-input');
        expect(input).toHaveValue('12.34');
    });
});
describe('PriceInput - Other States', function () {
    it('Displays basic error information', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD} label="Price" helpText="This is a help text" errorMessage="This is an error message" hasError={true}/>);
        var input = react_1.screen.getByTestId('price-input-error');
        expect(input).toHaveValue('12.34');
        expect(react_1.screen.getByTestId('price-input-error-error-message')).toHaveTextContent('This is an error message');
        expect(react_1.screen.queryByTestId('price-input-error-help-text')).not.toBeInTheDocument();
    });
    it('Displays help text if no error message', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD} label="Price" helpText="This is a help text" hasError={true}/>);
        var input = react_1.screen.getByTestId('price-input-error');
        expect(input).toHaveValue('12.34');
        expect(react_1.screen.queryByTestId('price-input-error-error-message')).not.toBeInTheDocument();
        expect(react_1.screen.getByTestId('price-input-error-help-text')).toHaveTextContent('This is a help text');
    });
    it('Displays disabled state', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD} label="Price" helpText="This is a help text" isDisabled={true}/>);
        var input = react_1.screen.getByTestId('price-input-disabled');
        expect(input).toHaveValue('12.34');
        expect(input).toBeDisabled();
    });
    it('Displays read-only state', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={1234} onPriceChange={function () { }} currency={sharedEnums_1.Currency.USD} label="Price" helpText="This is a help text" isReadOnly={true}/>);
        var input = react_1.screen.getByTestId('price-input-read-only');
        expect(input).toHaveValue('12.34');
        expect(input).toHaveAttribute('readonly');
    });
});
describe('PriceInput - Config', function () {
    it('Displays price correctly with non-us-standard configuration', function () {
        (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={123456789} onPriceChange={function () { }} currency={sharedEnums_1.Currency.EUR}/>);
        expect(react_1.screen.getByTestId('price-input')).toHaveValue('1.234.567,89');
        expect(react_1.screen.getByTestId('price-input-currency-symbol')).toHaveTextContent('€');
    });
});
describe('PriceInput - Interaction - Insert', function () {
    it('Select full input, enter 1, display should be 1.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 222;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('2.22');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '1', { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Move cursor to end, enter 1, display should be 1.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 0;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('0.00');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '1', {
                            initialSelectionStart: input.value.length,
                            initialSelectionEnd: input.value.length,
                        })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Move cursor to beginning, enter 1, display should be 1.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 0;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('0.00');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '1', { initialSelectionStart: 0, initialSelectionEnd: 0 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Move cursor to after decimal, enter 234, display should be 0.23', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 0;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('0.00');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '234', { initialSelectionStart: 2, initialSelectionEnd: 2 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('0.23');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Select full input, enter 123, display should be 123.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 0;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('0.00');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '123', { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('123.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Select full input, enter 1234.56, display should be 1,234.56', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 0;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('0.00');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '1234.56', { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1,234.56');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123, display should be 1.23, move cursor after decimal, enter 4, display should be 1.43', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '4', { initialSelectionStart: 2, initialSelectionEnd: 2 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.43');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123, display should be 1.23, move cursor to second to last place, enter 4, display should be 1.24', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '4', { initialSelectionStart: 3, initialSelectionEnd: 3 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.24');
                    return [2 /*return*/];
            }
        });
    }); });
});
describe('PriceInput - Interaction - Delete', function () {
    it('Set priceInCents to 123, display should be 1.23, select full input, delete, display should be 0.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('0.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Move cursor to beginning, delete, display should be 0.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 0, initialSelectionEnd: 0 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.23');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1234, display should be 12.34, move cursor to left of decimal, delete, display should be 1.34', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 2, initialSelectionEnd: 2 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.34');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1234, display should be 12.34, move cursor to right of decimal, delete, display should be 1.34', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 3, initialSelectionEnd: 3 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.34');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1234, display should be 12.34, move cursor between 1 and 2, delete, display should be 2.34', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 1, initialSelectionEnd: 1 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('2.34');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123456, display should be 1,234.56, move cursor to left of comma, delete, display should be 234.56', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123456;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1,234.56');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 1, initialSelectionEnd: 1 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('234.56');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123456, display should be 1,234.56, move cursor to right of comma, delete, display should be 234.56', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123456;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1,234.56');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 2, initialSelectionEnd: 2 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('234.56');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1234, display should be 12.23, move cursor to left of decimal, delete, display should be 1.34', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 2, initialSelectionEnd: 2 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.34');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1234, display should be 12.23, move cursor to right of decimal, delete, display should be 1.34', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 3, initialSelectionEnd: 3 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.34');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123, display should be 1.23, move cursor to end, delete, display should be 1.20', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, {
                            initialSelectionStart: input.value.length,
                            initialSelectionEnd: input.value.length,
                        })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.20');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123, display should be 1.23, move cursor to second to last, delete, display should be 1.30', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, { initialSelectionStart: 3, initialSelectionEnd: 3 })];
                case 2:
                    _a.sent();
                    expect(input).toHaveValue('1.30');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 123456, display should be 1,234.56, press delete all the way from end of string, display should be 0.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input, i;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123456;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1,234.56');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, {
                            initialSelectionStart: input.value.length,
                            initialSelectionEnd: input.value.length,
                        })];
                case 2:
                    _a.sent();
                    i = 0;
                    _a.label = 3;
                case 3:
                    if (!(i < input.value.length + 1)) return [3 /*break*/, 6];
                    return [4 /*yield*/, user.keyboard(KEY_BACKSPACE)];
                case 4:
                    _a.sent();
                    _a.label = 5;
                case 5:
                    i++;
                    return [3 /*break*/, 3];
                case 6:
                    expect(input).toHaveValue('0.00');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Set priceInCents to 1230, display should be 12.30, press delete twice, display should be 12.00', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1230;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.30');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, KEY_BACKSPACE, {
                            initialSelectionStart: input.value.length,
                            initialSelectionEnd: input.value.length,
                        })];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.keyboard(KEY_BACKSPACE)];
                case 3:
                    _a.sent();
                    expect(input).toHaveValue('12.00');
                    return [2 /*return*/];
            }
        });
    }); });
});
describe('PriceInput - Misc', function () {
    it('Enter letter, nothing should change', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, 'a', { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(handlePriceChange).not.toHaveBeenCalled();
                    expect(input).toHaveValue('1.23');
                    return [2 /*return*/];
            }
        });
    }); });
    it('onPriceChange is called with the correct value', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.type(input, '4', { initialSelectionStart: 0, initialSelectionEnd: input.value.length })];
                case 2:
                    _a.sent();
                    expect(handlePriceChange).toHaveBeenCalledWith(400);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Paste works with simple numbers', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.paste('400')];
                case 2:
                    _a.sent();
                    expect(handlePriceChange).toHaveBeenCalledWith(400);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Paste works with complex strings', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 123;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('1.23');
                    return [4 /*yield*/, user.click(input)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.paste('4,000.21')];
                case 2:
                    _a.sent();
                    expect(handlePriceChange).toHaveBeenCalledWith(400021);
                    return [2 /*return*/];
            }
        });
    }); });
    it('Typing . moves cursor to right of decimal', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.type(input, '5', { initialSelectionStart: 1, initialSelectionEnd: 1 })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.keyboard('.')];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.keyboard('6')];
                case 3:
                    _a.sent();
                    expect(input).toHaveValue('152.64');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Typing , moves cursor to right of decimal', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).toHaveValue('12.34');
                    return [4 /*yield*/, user.type(input, '5', { initialSelectionStart: 1, initialSelectionEnd: 1 })];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.keyboard(',')];
                case 2:
                    _a.sent();
                    return [4 /*yield*/, user.keyboard('6')];
                case 3:
                    _a.sent();
                    expect(input).toHaveValue('152.64');
                    return [2 /*return*/];
            }
        });
    }); });
    it('Clicking on currency symbol focuses input', function () { return __awaiter(void 0, void 0, void 0, function () {
        var price, handlePriceChange, user, input;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    price = 1234;
                    handlePriceChange = vi.fn(function (newPrice) {
                        price = newPrice;
                    });
                    (0, react_1.render)(<PriceInput_1.PriceInput priceInCents={price} onPriceChange={handlePriceChange} currency={sharedEnums_1.Currency.USD}/>);
                    user = user_event_1.default.setup();
                    input = react_1.screen.getByTestId('price-input');
                    expect(input).not.toHaveFocus();
                    return [4 /*yield*/, user.click(react_1.screen.getByTestId('price-input-currency-symbol'))];
                case 1:
                    _a.sent();
                    expect(input).toHaveFocus();
                    return [2 /*return*/];
            }
        });
    }); });
});
