"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StandardInput = void 0;
var preview_api_1 = require("storybook/preview-api");
var sharedEnums_1 = require("../../enums/sharedEnums");
var PriceInput_1 = require("./PriceInput");
exports.default = {
    title: 'ticketing-shared/PriceInput',
    component: PriceInput_1.PriceInput,
    argTypes: {
        currency: {
            name: 'Currency',
            control: {
                type: 'select',
                labels: {
                    USD: 'USD',
                    CAD: 'CAD',
                    EUR: 'EUR',
                },
            },
            options: [sharedEnums_1.Currency.USD, sharedEnums_1.Currency.CAD, sharedEnums_1.Currency.EUR],
        },
        priceInCents: {
            name: 'Price (in cents)',
            control: 'number',
        },
        onPriceChange: {
            table: {
                disable: true,
            },
        },
        onFocus: {
            table: {
                disable: true,
            },
        },
        onBlur: {
            table: {
                disable: true,
            },
        },
        qaId: {
            table: {
                disable: true,
            },
        },
        label: {
            name: 'Label',
            control: 'text',
        },
        helpText: {
            name: 'Help Text',
            control: 'text',
        },
        errorMessage: {
            name: 'Error Message',
            control: 'text',
        },
        isRequired: {
            name: 'Is Required',
            control: 'boolean',
        },
        isDisabled: {
            name: 'Is Disabled',
            control: 'boolean',
        },
        isReadOnly: {
            name: 'Is Read Only',
            control: 'boolean',
        },
        hasError: {
            name: 'Has Error',
            control: 'boolean',
        },
        maximumDigits: {
            name: 'Maximum Digits',
            control: 'number',
            description: 'Maximum number of digits allowed in the input',
        },
    },
    parameters: {
        chromatic: { disableSnapshot: false },
        controls: {
            sort: 'custom',
        },
    },
};
var PriceInputTemplate = function () {
    var _a = (0, preview_api_1.useArgs)(), _b = _a[0], currency = _b.currency, priceInCents = _b.priceInCents, label = _b.label, helpText = _b.helpText, isRequired = _b.isRequired, isDisabled = _b.isDisabled, isReadOnly = _b.isReadOnly, hasError = _b.hasError, errorMessage = _b.errorMessage, maximumDigits = _b.maximumDigits, updateArgs = _a[1];
    var onPriceChange = function (newPriceInCents) {
        updateArgs({ priceInCents: newPriceInCents });
    };
    return (<PriceInput_1.PriceInput currency={currency} label={label} helpText={helpText} maximumDigits={maximumDigits} priceInCents={priceInCents} onPriceChange={onPriceChange} isRequired={isRequired} isDisabled={isDisabled} isReadOnly={isReadOnly} hasError={hasError} errorMessage={errorMessage}/>);
};
exports.StandardInput = {
    render: PriceInputTemplate,
    args: {
        currency: sharedEnums_1.Currency.USD,
        priceInCents: 0,
        maximumDigits: 9,
        currencySymbol: '$',
        label: 'Price',
        helpText: 'This is your ticket price.',
        errorMessage: 'This is an error message',
        hasError: false,
        isDisabled: false,
        isReadOnly: false,
        isRequired: false,
        decimalPlaces: 2,
        thousandSeparator: ',',
        decimalSeparator: '.',
    },
};
