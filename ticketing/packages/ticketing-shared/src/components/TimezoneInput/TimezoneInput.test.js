"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("react");
var react_2 = require("@testing-library/react");
var user_event_1 = require("@testing-library/user-event");
var renderHelpers_1 = require("../../test/renderHelpers");
var TimezoneInput_1 = require("./TimezoneInput");
vi.mock('react-timezone-select', function () { return ({
    useTimezoneSelect: vi.fn(function () {
        return {
            options: [
                { value: 'Pacific/Honolulu', label: '(GMT-10:00) Hawaii (HAST)' },
                { value: 'America/Juneau', label: '(GMT-8:00) Alaska (AKDT)' },
                { value: 'America/Toronto', label: '(GMT-4:00) Toronto (EDT)' },
            ],
        };
    }),
    allTimezones: {
        'Pacific/Honolulu': 'Hawaii',
        'America/Juneau': 'Alaska',
        'America/Toronto': 'Toronto',
        'Europe/London': 'London', // Should be filtered out
    },
}); });
describe('TimezoneInput tests', function () {
    var mockOnChange = vi.fn();
    beforeEach(function () {
        mockOnChange.mockClear();
    });
    it('renders with required props', function () {
        (0, renderHelpers_1.renderWithOptions)(<TimezoneInput_1.TimezoneInput timeZoneIdentifier={undefined} onTimezoneChange={mockOnChange}/>, {
            withIntlProvider: true,
        });
        expect(react_2.screen.getByTestId('time-zone-identifier-input-select')).toBeInTheDocument();
    });
    it('displays pre-selected timezone when provided', function () {
        (0, renderHelpers_1.renderWithOptions)(<TimezoneInput_1.TimezoneInput timeZoneIdentifier="America/Toronto" onTimezoneChange={mockOnChange}/>, {
            withIntlProvider: true,
        });
        expect(react_2.screen.getByTestId('time-zone-identifier-input-select')).toHaveTextContent('(GMT-4:00) Toronto (EDT)');
    });
    it('calls onTimezoneChange when selection changes', function () { return __awaiter(void 0, void 0, void 0, function () {
        var timezoneInput, user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<TimezoneInput_1.TimezoneInput timeZoneIdentifier={undefined} onTimezoneChange={mockOnChange}/>, {
                        withIntlProvider: true,
                    });
                    timezoneInput = react_2.screen.getByTestId('time-zone-identifier-input-select');
                    user = user_event_1.default.setup({ delay: null });
                    return [4 /*yield*/, user.click(timezoneInput)];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, user.click(react_2.screen.getByTestId('time-zone-identifier-input-Pacific/Honolulu-option'))];
                case 2:
                    _a.sent();
                    expect(mockOnChange).toHaveBeenCalledWith({
                        value: 'Pacific/Honolulu',
                        label: '(GMT-10:00) Hawaii (HAST)',
                    });
                    return [2 /*return*/];
            }
        });
    }); });
    it('only shows US and Canadian timezone options', function () { return __awaiter(void 0, void 0, void 0, function () {
        var timezoneInput, user;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    (0, renderHelpers_1.renderWithOptions)(<TimezoneInput_1.TimezoneInput timeZoneIdentifier={undefined} onTimezoneChange={mockOnChange}/>, {
                        withIntlProvider: true,
                    });
                    timezoneInput = react_2.screen.getByTestId('time-zone-identifier-input-select');
                    user = user_event_1.default.setup({ delay: null });
                    return [4 /*yield*/, user.click(timezoneInput)];
                case 1:
                    _a.sent();
                    // Should show US/CA timezones
                    expect(react_2.screen.getByTestId('time-zone-identifier-input-Pacific/Honolulu-option')).toBeInTheDocument();
                    expect(react_2.screen.getByTestId('time-zone-identifier-input-America/Toronto-option')).toBeInTheDocument();
                    // Should not show European timezones
                    expect(react_2.screen.queryByText(/London/)).not.toBeInTheDocument();
                    return [2 /*return*/];
            }
        });
    }); });
});
