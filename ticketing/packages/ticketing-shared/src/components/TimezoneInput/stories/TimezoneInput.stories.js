"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Demo = void 0;
var react_1 = require("react");
var TimezoneInput_1 = require("../TimezoneInput");
exports.default = {
    title: 'General/Timezone Input', // This story will come under the "General" category
    component: TimezoneInput_1.TimezoneInput, // This story implements the "TimezoneInput" component
};
exports.Demo = {
    render: function () {
        var _a = (0, react_1.useState)(''), timeZone = _a[0], setTimeZone = _a[1];
        return (<TimezoneInput_1.TimezoneInput timeZoneIdentifier={timeZone} selectInputPlaceholder="Select your timezone" onTimezoneChange={function (newValue) {
                setTimeZone(newValue.value);
            }}/>);
    },
};
