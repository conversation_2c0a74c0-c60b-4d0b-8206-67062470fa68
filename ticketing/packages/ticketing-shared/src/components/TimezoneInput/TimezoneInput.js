"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.caIanaTimeZones = exports.usIanaTimeZones = void 0;
exports.TimezoneInput = TimezoneInput;
var react_timezone_select_1 = require("react-timezone-select");
var uniform_web_1 = require("@hudl/uniform-web");
var frontends_i18n_1 = require("frontends-i18n");
exports.usIanaTimeZones = [
    'Pacific/Honolulu',
    'America/Juneau',
    'America/Los_Angeles',
    'America/Phoenix',
    'America/Boise',
    'America/Chicago',
    'America/Detroit',
];
exports.caIanaTimeZones = [
    'America/Vancouver',
    'America/Whitehorse',
    'America/Edmonton',
    'America/Regina',
    'America/Toronto',
    'America/Halifax',
    'America/St_Johns',
];
function TimezoneInput(props) {
    var style = props.style, timeZoneIdentifier = props.timeZoneIdentifier, onTimezoneChange = props.onTimezoneChange, _a = props.selectInputPlaceholder, selectInputPlaceholder = _a === void 0 ? 'Select timezone' : _a, isReadOnly = props.isReadOnly;
    var labelStyle = 'abbrev';
    var timezones = Object.fromEntries(Object.entries(react_timezone_select_1.allTimezones).filter(function (_a) {
        var tzKey = _a[0];
        return exports.usIanaTimeZones.includes(tzKey) || exports.caIanaTimeZones.includes(tzKey);
    }));
    var options = (0, react_timezone_select_1.useTimezoneSelect)({ timezones: timezones, labelStyle: labelStyle }).options;
    var selectOptions = options.map(function (option) { return ({ label: option.label, value: option.value }); });
    var selected;
    if (timeZoneIdentifier) {
        selected = selectOptions.find(function (option) { return option.value === timeZoneIdentifier; });
    }
    else {
        selected = null;
    }
    return (<uniform_web_1.Select captureMenuScroll={false} className={style} qaId="time-zone-identifier-input" label={(0, frontends_i18n_1.formatMessage)({ id: 'ticketing-shared.timezone-selection.label' })} onChange={function (newValue) { return onTimezoneChange(newValue); }} value={selected} options={selectOptions} placeholder={selectInputPlaceholder} isRequired isSearchable={false} isReadOnly={isReadOnly}/>);
}
