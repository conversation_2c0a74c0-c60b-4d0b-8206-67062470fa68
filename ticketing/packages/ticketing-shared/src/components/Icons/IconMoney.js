"use strict";
/* eslint-disable react/jsx-props-no-spreading -- spreading props for icon */
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.IconMoney = IconMoney;
/* eslint-disable max-len -- icon path */
var uniform_web_1 = require("@hudl/uniform-web");
function IconMoney(_a) {
    var className = _a.className, color = _a.color, _b = _a.size, size = _b === void 0 ? 'medium' : _b, title = _a.title, qaId = _a.qaId, rest = __rest(_a, ["className", "color", "size", "title", "qaId"]);
    return (<uniform_web_1.Icon viewBox="0 0 32 32" size={size} color={color} title={title || 'Money'} className={className} qaId={qaId} {...rest}>
      <path d="M18.6668 17.3333C17.5557 17.3333 16.6113 16.9444 15.8335 16.1666C15.0557 15.3888 14.6668 14.4444 14.6668 13.3333C14.6668 12.2221 15.0557 11.2777 15.8335 10.4999C16.6113 9.72214 17.5557 9.33325 18.6668 9.33325C19.7779 9.33325 20.7224 9.72214 21.5002 10.4999C22.2779 11.2777 22.6668 12.2221 22.6668 13.3333C22.6668 14.4444 22.2779 15.3888 21.5002 16.1666C20.7224 16.9444 19.7779 17.3333 18.6668 17.3333ZM9.3335 21.3333C8.60016 21.3333 7.97239 21.0721 7.45016 20.5499C6.92794 20.0277 6.66683 19.3999 6.66683 18.6666V7.99992C6.66683 7.26659 6.92794 6.63881 7.45016 6.11659C7.97239 5.59436 8.60016 5.33325 9.3335 5.33325H28.0002C28.7335 5.33325 29.3613 5.59436 29.8835 6.11659C30.4057 6.63881 30.6668 7.26659 30.6668 7.99992V18.6666C30.6668 19.3999 30.4057 20.0277 29.8835 20.5499C29.3613 21.0721 28.7335 21.3333 28.0002 21.3333H9.3335ZM12.0002 18.6666H25.3335C25.3335 17.9333 25.5946 17.3055 26.1168 16.7833C26.6391 16.261 27.2668 15.9999 28.0002 15.9999V10.6666C27.2668 10.6666 26.6391 10.4055 26.1168 9.88325C25.5946 9.36103 25.3335 8.73325 25.3335 7.99992H12.0002C12.0002 8.73325 11.7391 9.36103 11.2168 9.88325C10.6946 10.4055 10.0668 10.6666 9.3335 10.6666V15.9999C10.0668 15.9999 10.6946 16.261 11.2168 16.7833C11.7391 17.3055 12.0002 17.9333 12.0002 18.6666ZM26.6668 26.6666H4.00016C3.26683 26.6666 2.63905 26.4055 2.11683 25.8833C1.59461 25.361 1.3335 24.7333 1.3335 23.9999V9.33325H4.00016V23.9999H26.6668V26.6666Z" fill="#E8EAED"/>
    </uniform_web_1.Icon>);
}
