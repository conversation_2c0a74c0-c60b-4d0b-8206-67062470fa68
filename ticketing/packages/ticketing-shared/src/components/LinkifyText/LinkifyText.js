"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinkifyText = LinkifyText;
var react_1 = require("react");
var linkify_react_1 = require("linkify-react");
var uniform_web_1 = require("@hudl/uniform-web");
function LinkifyText(_a) {
    var text = _a.text, _b = _a.openLinkInNewTab, openLinkInNewTab = _b === void 0 ? true : _b, hrefClassName = _a.hrefClassName, rest = __rest(_a, ["text", "openLinkInNewTab", "hrefClassName"]);
    var linkifyOptions = (0, react_1.useMemo)(function () {
        // default any links to use https if they are not specified
        var options = { defaultProtocol: 'https' };
        if (openLinkInNewTab) {
            options.target = '_blank';
        }
        options.className = hrefClassName !== null && hrefClassName !== void 0 ? hrefClassName : 'u-link u-link--article';
        return options;
    }, [openLinkInNewTab, hrefClassName]);
    return (<uniform_web_1.Text {...rest}>
      <linkify_react_1.default as="span" options={linkifyOptions}>
        {text}
      </linkify_react_1.default>
    </uniform_web_1.Text>);
}
