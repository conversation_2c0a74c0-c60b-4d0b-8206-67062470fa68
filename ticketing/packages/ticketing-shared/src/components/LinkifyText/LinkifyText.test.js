"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var react_1 = require("@testing-library/react");
var renderHelpers_1 = require("../../test/renderHelpers");
var LinkifyText_1 = require("../LinkifyText/LinkifyText");
describe('LinkifyText', function () {
    var qaId = 'linkify-text-qa-id';
    it('renders plain text without links correctly', function () {
        (0, renderHelpers_1.renderWithOptions)(<LinkifyText_1.LinkifyText text="This is a test" qaId={qaId}/>);
        expect(react_1.screen.getByTestId(qaId)).toBeInTheDocument();
    });
    it('renders a link when the text contains a URL', function () {
        (0, renderHelpers_1.renderWithOptions)(<LinkifyText_1.LinkifyText text="Check this out: https://example.com" qaId={qaId}/>);
        var linkElement = react_1.screen.queryByRole('link');
        expect(linkElement).toBeInTheDocument();
        expect(linkElement).toHaveAttribute('href', 'https://example.com');
    });
    it('opens links in a new tab when openLinkInNewTab is true', function () {
        (0, renderHelpers_1.renderWithOptions)(<LinkifyText_1.LinkifyText text="Visit https://example.com" openLinkInNewTab qaId={qaId}/>);
        var linkElement = react_1.screen.getByRole('link');
        expect(linkElement).toHaveAttribute('target', '_blank');
    });
    it('does not open links in a new tab when openLinkInNewTab is false', function () {
        (0, renderHelpers_1.renderWithOptions)(<LinkifyText_1.LinkifyText text="Visit https://example.com" openLinkInNewTab={false} qaId={qaId}/>);
        var linkElement = react_1.screen.getByRole('link');
        expect(linkElement).not.toHaveAttribute('target', '_blank');
    });
    it('renders multiple links correctly', function () {
        (0, renderHelpers_1.renderWithOptions)(<LinkifyText_1.LinkifyText text="Visit https://example.com and https://test.com" qaId={qaId}/>);
        var links = react_1.screen.queryAllByRole('link');
        expect(links).toHaveLength(2);
        expect(links[0]).toHaveAttribute('href', 'https://example.com');
        expect(links[1]).toHaveAttribute('href', 'https://test.com');
    });
});
