"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fallbackTimezone = exports.formDescriptionMaxLength = exports.formTitleMaxLength = exports.minimumPassPriceForBundledFeesInCents = exports.ticketTypeNameMaxLength = exports.minimumTicketPriceForBundledFeesInCents = exports.dollarCentsConversion = exports.quantityInputMaxLength = exports.emptyTicketType = void 0;
exports.emptyTicketType = {
    id: '',
    name: '',
    organizationId: '',
    createdAt: undefined,
    priceInCents: 0,
    updatedAt: undefined,
    ticketTypeId: '',
};
exports.quantityInputMaxLength = 9;
exports.dollarCentsConversion = 100;
exports.minimumTicketPriceForBundledFeesInCents = 200;
exports.ticketTypeNameMaxLength = 60;
exports.minimumPassPriceForBundledFeesInCents = 400;
exports.formTitleMaxLength = 150;
exports.formDescriptionMaxLength = 2000;
exports.fallbackTimezone = 'Etc/UTC';
