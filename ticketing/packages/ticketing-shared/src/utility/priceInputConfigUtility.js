"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPriceInputConfigForCurrency = getPriceInputConfigForCurrency;
var sharedEnums_1 = require("../enums/sharedEnums");
var usDollarPriceInputConfig = {
    currencySymbol: '$',
    decimalPlaces: 2,
    decimalSeparator: '.',
    thousandSeparator: ',',
};
var canadianDollarPriceInputConfig = {
    currencySymbol: 'C$',
    decimalPlaces: 2,
    decimalSeparator: '.',
    thousandSeparator: ',',
};
var euroPriceInputConfig = {
    currencySymbol: '€',
    decimalPlaces: 2,
    decimalSeparator: ',',
    thousandSeparator: '.',
};
function getPriceInputConfigForCurrency(currency) {
    switch (currency) {
        case sharedEnums_1.Currency.USD:
            return usDollarPriceInputConfig;
        case sharedEnums_1.Currency.CAD:
            return canadianDollarPriceInputConfig;
        case sharedEnums_1.Currency.EUR:
            return euroPriceInputConfig;
        default:
            return usDollarPriceInputConfig;
    }
}
