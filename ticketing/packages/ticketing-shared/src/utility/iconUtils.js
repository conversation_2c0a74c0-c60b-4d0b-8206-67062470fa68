"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getFileTypeIconForExtension = getFileTypeIconForExtension;
var uniform_web_1 = require("@hudl/uniform-web");
function getFileTypeIconForExtension(extension) {
    switch (extension) {
        case 'pdf':
            return <uniform_web_1.IconFiletypePdf />;
        case 'doc':
        case 'docx':
        case 'odt':
        case 'rtf':
        case 'txt':
            return <uniform_web_1.IconFiletypeDocument />;
        case 'csv':
        case 'xlsx':
        case 'xls':
            return <uniform_web_1.IconFiletypeSpreadsheet />;
        case 'jpg':
        case 'jpeg':
        case 'png':
        case 'gif':
        case 'bmp':
            return <uniform_web_1.IconFiletypeImage />;
        default:
            return <uniform_web_1.IconFiletypeOther />;
    }
}
