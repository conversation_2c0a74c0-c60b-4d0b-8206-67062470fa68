"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.centsToDollars = exports.formatPriceInCentsFromDollarString = void 0;
var constants_1 = require("../types/constants");
var formatPriceInCentsFromDollarString = function (priceInDollars) {
    var priceInCents = parseFloat(priceInDollars) * constants_1.dollarCentsConversion;
    return priceInCents;
};
exports.formatPriceInCentsFromDollarString = formatPriceInCentsFromDollarString;
var centsToDollars = function (cents) {
    return cents / constants_1.dollarCentsConversion;
};
exports.centsToDollars = centsToDollars;
