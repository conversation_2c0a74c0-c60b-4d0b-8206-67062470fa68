"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPaymentPlatformLoginUrl = exports.getPaymentPlatformOnboardingUrl = exports.getTicketingHomeUrl = void 0;
var environmentUtils_1 = require("./environmentUtils");
var PaymentPlatformLinkType;
(function (PaymentPlatformLinkType) {
    PaymentPlatformLinkType["OnboardAccount"] = "onboard_account";
    PaymentPlatformLinkType["Login"] = "login";
})(PaymentPlatformLinkType || (PaymentPlatformLinkType = {}));
var PaymentPlatformQueryParams;
(function (PaymentPlatformQueryParams) {
    PaymentPlatformQueryParams["ReturnUrl"] = "returnUrl";
})(PaymentPlatformQueryParams || (PaymentPlatformQueryParams = {}));
var getOrigin = function () {
    var hostname = window.location.hostname;
    var hostParts = hostname.replace('admin.', '').split('.');
    var domain = hostParts[hostParts.length - 2];
    if ((0, environmentUtils_1.isProductionEnvironment)()) {
        return "https://".concat(domain, ".com");
    }
    var branch = hostParts[0];
    return "https://".concat(branch, ".").concat(domain, ".com");
};
var getTicketingHomeUrl = function (organizationId) {
    var pathname = "app/ticketing/".concat(organizationId);
    return "".concat(getOrigin(), "/").concat(pathname);
};
exports.getTicketingHomeUrl = getTicketingHomeUrl;
var getPaymentPlatformOnboardingUrl = function (organizationId, returnUrl) {
    var _a;
    var pathname = "app/payments/platform/".concat(PaymentPlatformLinkType.OnboardAccount, "/").concat(organizationId);
    var queryParams = new URLSearchParams((_a = {},
        _a[PaymentPlatformQueryParams.ReturnUrl] = returnUrl,
        _a));
    return "".concat(getOrigin(), "/").concat(pathname, "?").concat(queryParams.toString());
};
exports.getPaymentPlatformOnboardingUrl = getPaymentPlatformOnboardingUrl;
var getPaymentPlatformLoginUrl = function (organizationId, returnUrl) {
    var _a;
    var pathname = "app/payments/platform/".concat(PaymentPlatformLinkType.Login, "/").concat(organizationId);
    var queryParams = new URLSearchParams((_a = {},
        _a[PaymentPlatformQueryParams.ReturnUrl] = returnUrl,
        _a));
    return "".concat(getOrigin(), "/").concat(pathname, "?").concat(queryParams.toString());
};
exports.getPaymentPlatformLoginUrl = getPaymentPlatformLoginUrl;
