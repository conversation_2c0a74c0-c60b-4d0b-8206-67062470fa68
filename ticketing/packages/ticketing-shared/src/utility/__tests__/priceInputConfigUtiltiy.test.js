"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var vitest_1 = require("vitest");
var sharedEnums_1 = require("../../enums/sharedEnums");
var priceInputConfigUtility_1 = require("../priceInputConfigUtility");
(0, vitest_1.describe)('getPriceInputConfigForCurrency', function () {
    (0, vitest_1.it)('returns USD config for Currency.USD', function () {
        var config = (0, priceInputConfigUtility_1.getPriceInputConfigForCurrency)(sharedEnums_1.Currency.USD);
        (0, vitest_1.expect)(config).toEqual({
            currencySymbol: '$',
            decimalPlaces: 2,
            decimalSeparator: '.',
            thousandSeparator: ',',
        });
    });
    (0, vitest_1.it)('returns CAD config for Currency.CAD', function () {
        var config = (0, priceInputConfigUtility_1.getPriceInputConfigForCurrency)(sharedEnums_1.Currency.CAD);
        (0, vitest_1.expect)(config).toEqual({
            currencySymbol: 'C$',
            decimalPlaces: 2,
            decimalSeparator: '.',
            thousandSeparator: ',',
        });
    });
    (0, vitest_1.it)('returns USD config for unknown currency', function () {
        // @ts-expect-error testing fallback for unknown currency
        var config = (0, priceInputConfigUtility_1.getPriceInputConfigForCurrency)('FAKE');
        (0, vitest_1.expect)(config).toEqual({
            currencySymbol: '$',
            decimalPlaces: 2,
            decimalSeparator: '.',
            thousandSeparator: ',',
        });
    });
    (0, vitest_1.it)('returns USD config for undefined', function () {
        // @ts-expect-error testing fallback for undefined
        var config = (0, priceInputConfigUtility_1.getPriceInputConfigForCurrency)(undefined);
        (0, vitest_1.expect)(config).toEqual({
            currencySymbol: '$',
            decimalPlaces: 2,
            decimalSeparator: '.',
            thousandSeparator: ',',
        });
    });
});
