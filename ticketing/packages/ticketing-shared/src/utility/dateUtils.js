"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentDateString = exports.isDatePast = exports.getDateWithoutTime = exports.getZonedDateTimeFromUtc = exports.getDate = exports.getNowDateWithoutTime = void 0;
var date_fns_tz_1 = require("date-fns-tz");
var constants_1 = require("../types/constants");
var getNowDateWithoutTime = function () {
    var nowDate = new Date();
    return new Date(nowDate.getFullYear(), nowDate.getMonth(), nowDate.getDate());
};
exports.getNowDateWithoutTime = getNowDateWithoutTime;
var getDate = function (date) {
    return new Date(date);
};
exports.getDate = getDate;
var getZonedDateTimeFromUtc = function (dateTime, timeZoneIdentifier) {
    if (!timeZoneIdentifier)
        return dateTime;
    return (0, date_fns_tz_1.utcToZonedTime)(dateTime, timeZoneIdentifier);
};
exports.getZonedDateTimeFromUtc = getZonedDateTimeFromUtc;
var getDateWithoutTime = function (date, timeZoneIdentifier) {
    var zoneDate = (0, exports.getZonedDateTimeFromUtc)((0, exports.getDate)(date), timeZoneIdentifier !== null && timeZoneIdentifier !== void 0 ? timeZoneIdentifier : constants_1.fallbackTimezone);
    return new Date(zoneDate.getFullYear(), zoneDate.getMonth(), zoneDate.getDate());
};
exports.getDateWithoutTime = getDateWithoutTime;
var isDatePast = function (date, timeZoneIdentifier) {
    var dateWithoutTime = (0, exports.getDateWithoutTime)(date, timeZoneIdentifier);
    var nowWithoutTime = (0, exports.getNowDateWithoutTime)();
    return dateWithoutTime < nowWithoutTime;
};
exports.isDatePast = isDatePast;
var getCurrentDateString = function () {
    return new Date().toISOString().split('T')[0];
};
exports.getCurrentDateString = getCurrentDateString;
