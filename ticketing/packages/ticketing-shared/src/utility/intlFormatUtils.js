"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getKeyForTicketingEntityType = getKeyForTicketingEntityType;
var sharedEnums_1 = require("../enums/sharedEnums");
function getKeyForTicketingEntityType(intlKey, ticketingEntityType, pluralize) {
    if (pluralize === void 0) { pluralize = false; }
    var key;
    switch (ticketingEntityType) {
        case sharedEnums_1.TicketingEntityType.Ticket:
            key = "".concat(intlKey, "-tickets");
            break;
        case sharedEnums_1.TicketingEntityType.Pass:
            key = "".concat(intlKey, "-passes");
            break;
        default:
            return intlKey;
    }
    if (pluralize) {
        key += '-plural';
    }
    return key;
}
