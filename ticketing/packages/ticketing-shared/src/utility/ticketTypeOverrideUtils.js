"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildTicketTypesWithOverrides = buildTicketTypesWithOverrides;
exports.getPriceOfTicketTypeWithOverrides = getPriceOfTicketTypeWithOverrides;
exports.getQuantityOfTicketTypeWithOverrides = getQuantityOfTicketTypeWithOverrides;
function buildTicketTypesWithOverrides(ticketTypes, ticketTypeReferences) {
    var ticketTypeReferencesDict = Object.fromEntries((ticketTypeReferences === null || ticketTypeReferences === void 0 ? void 0 : ticketTypeReferences.map(function (ttr) { return [ttr === null || ttr === void 0 ? void 0 : ttr.ticketTypeId, ttr]; })) || []);
    return ticketTypes === null || ticketTypes === void 0 ? void 0 : ticketTypes.map(function (tt) {
        var _a, _b;
        return {
            id: tt.id,
            name: tt.name,
            organizationId: tt.organizationId,
            priceInCents: tt.priceInCents,
            quantity: tt.quantity,
            priceOverride: (_a = ticketTypeReferencesDict[tt.id]) === null || _a === void 0 ? void 0 : _a.priceOverride,
            quantityOverride: (_b = ticketTypeReferencesDict[tt.id]) === null || _b === void 0 ? void 0 : _b.quantityOverride,
            updatedAt: tt.updatedAt,
        };
    });
}
function getPriceOfTicketTypeWithOverrides(ticketType) {
    var _a;
    return (_a = ticketType === null || ticketType === void 0 ? void 0 : ticketType.priceOverride) !== null && _a !== void 0 ? _a : ticketType === null || ticketType === void 0 ? void 0 : ticketType.priceInCents;
}
function getQuantityOfTicketTypeWithOverrides(ticketType) {
    var _a;
    return (_a = ticketType === null || ticketType === void 0 ? void 0 : ticketType.quantityOverride) !== null && _a !== void 0 ? _a : ticketType === null || ticketType === void 0 ? void 0 : ticketType.quantity;
}
