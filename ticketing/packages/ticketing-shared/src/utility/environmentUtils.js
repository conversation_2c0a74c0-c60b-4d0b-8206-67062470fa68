"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isProductionEnvironment = exports.getEnvironment = exports.Environment = void 0;
var Environment;
(function (Environment) {
    Environment["Thor"] = "thorhudl";
    Environment["Prod"] = "hudl";
})(Environment || (exports.Environment = Environment = {}));
var getEnvironment = function () {
    var hostname = window.location.hostname;
    var hostParts = hostname.replace('admin.', '').split('.');
    var domain = hostParts[hostParts.length - 2];
    if (domain === Environment.Prod) {
        return Environment.Prod;
    }
    return Environment.Thor;
};
exports.getEnvironment = getEnvironment;
var isProductionEnvironment = function () {
    return (0, exports.getEnvironment)() === Environment.Prod;
};
exports.isProductionEnvironment = isProductionEnvironment;
