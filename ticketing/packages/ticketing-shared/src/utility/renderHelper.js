"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderWithOptions = void 0;
exports.intlWrapper = intlWrapper;
var react_1 = require("react");
var react_2 = require("@testing-library/react");
var react_intl_1 = require("react-intl");
var uniform_web_1 = require("@hudl/uniform-web");
var i18n_json_1 = require("../../i18n.json");
var renderWithOptions = function (children, options) {
    var withIntlProvider = (options === null || options === void 0 ? void 0 : options.withIntlProvider) ? (<react_intl_1.IntlProvider locale="en" messages={i18n_json_1.default['base-language']} onError={vi.fn()}>
      {children}
    </react_intl_1.IntlProvider>) : (children);
    return (0, react_2.render)(<uniform_web_1.TooltipProvider>{withIntlProvider}</uniform_web_1.TooltipProvider>);
};
exports.renderWithOptions = renderWithOptions;
function intlWrapper(_a) {
    var children = _a.children;
    return (<react_intl_1.IntlProvider locale="en" messages={i18n_json_1.default['base-language']} defaultLocale="en">
      {children}
    </react_intl_1.IntlProvider>);
}
