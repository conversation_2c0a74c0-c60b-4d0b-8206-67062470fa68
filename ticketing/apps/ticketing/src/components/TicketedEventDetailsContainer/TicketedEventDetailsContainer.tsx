import { useEffect, useMemo, useState } from 'react';

import { useReactiveVar } from '@apollo/client';
import { useNavigate, useParams } from 'react-router-dom';

import { Logger } from '@hudl/frontends-logging';
import { Headline, IconUiMore, IconUiNavigationBack, Note, Spinner } from '@hudl/uniform-web';
import { ActionList } from '@hudl/uniform-web-actions-legacy';
import { Button } from '@hudl/uniform-web-button-legacy';
import { ToastMessenger } from '@hudl/uniform-web-notifications-legacy';
import { formatMessage } from 'frontends-i18n';

import { LoggingAttributes } from '../../enums/loggingAttributes';
import { ItemType, LinkedEntryType } from '../../enums/shared';
import { TicketedEventDetailsContainerTabs, TicketingManagementTabs } from '../../enums/tabEnums';
import {
  TicketGroup,
  TicketGroupsForTicketedEventsInput,
  TicketGroupSortType,
} from '../../graphql/generated/graphqlTypes';
import useGetAllTicketGroupsForTicketedEvent from '../../graphql/hooks/useGetAllTicketGroupsForTicketedEvent';
import useTicketedEventByIdWithAnalytics from '../../graphql/hooks/useTicketedEventByIdWithAnalytics';
import { IconTicket } from '../../icons/IconTicket';
import { assertNever } from '../../utility/assertNever';
import { loggingParams } from '../../utility/constants';
import { isDatePast } from '../../utility/dateTimeUtils';
import {
  selectedChildHomeTab,
  selectedFormStep,
  selectedTicketedEventDetailsContainerTab,
} from '../../utility/stateVars';
import { useEventFormStepIndicators } from '../../utility/ticketedEventFormStepUtils/EventFormContentUtils';
import { isTicketedEventUpcoming } from '../../utility/ticketedEventUtils';
import { splitComplimentaryTicketGroups } from '../../utility/ticketGroupUtils';
import { buildAddTicketingLink, buildReportCashSalesLink, getOrderUrl } from '../../utility/urlUtils';
import { reloadPage } from '../../utility/windowUtils';
import PurchaserList from '../PurchaserList/PurchaserList';
import ShareLinkModal from '../ShareLinkModal/ShareLinkModal';
import { TabProps } from '../tabSwitcher/Tab/Tab';
import { TabSwitcher } from '../tabSwitcher/TabSwitcher/TabSwitcher';
import TicketedEventDetailsOverview from '../TicketedEventDetailsOverview/TicketedEventDetailsOverview';

import styles from './TicketedEventDetailsContainer.module.scss';

function TicketedEventDetailsContainer() {
  const logger = useMemo(() => new Logger('Ticketing'), []);
  // Hides any global toasts that may be rendered from previous pages
  useEffect(() => {
    ToastMessenger.hide();
  });

  useEffect(() => {
    logger.log('Viewed Ticketed Event Details', {
      [LoggingAttributes.FUNC_ATTRIBUTE]: loggingParams.func.view,
      [LoggingAttributes.OP_ATTRIBUTE]: loggingParams.op.ticketedEvent,
      [LoggingAttributes.PAGE_ATTRIBUTE]: loggingParams.page.ticketedEventDetailsPage,
      [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
    });
  }, [logger]);

  const { organizationId, ticketedEventId } = useParams();
  const navigate = useNavigate();
  const selectedTab = useReactiveVar(selectedTicketedEventDetailsContainerTab);
  const lastStep = useEventFormStepIndicators().length - 1;

  const [ticketGroups, setTicketGroups] = useState<TicketGroup[]>([]);
  const [eventDetailsLastUpdated, setEventDetailsLastUpdated] = useState<Date | undefined>();
  const [ticketGroupsLastUpdated, setTicketGroupsLastUpdated] = useState<Date | undefined>();
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);

  const { ticketedEvent, ticketedEventLoading, ticketedEventError } = useTicketedEventByIdWithAnalytics(
    ticketedEventId!,
    {
      onCompleted() {
        setEventDetailsLastUpdated(new Date());
      },
      fetchPolicy: 'network-only',
    }
  );

  const isPastEvent = isDatePast(ticketedEvent?.date, ticketedEvent?.timeZoneIdentifier);

  const navigateBack = () => {
    if (isPastEvent) {
      selectedChildHomeTab(TicketingManagementTabs.PastTicketedEvents);
    }
    navigate(`/ticketing/${organizationId}`);
  };

  const renderLoading = () => {
    return (
      <div className={styles.errorAndLoadingContainer}>
        <Spinner size="large" qaId="event-details-page-spinner" />
      </div>
    );
  };

  const renderError = () => {
    return (
      <div className={styles.errorAndLoadingContainer} data-qa-id="event-details-error">
        <Note type="critical" size="large" className={styles.errorNote}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.error.note' })}
        </Note>
        <Button buttonType="subtle" onClick={reloadPage} qaId="event-details-page-reload-button">
          {formatMessage({ id: 'ticketing.reload' })}
        </Button>
      </div>
    );
  };

  const navigateToCompTicketing = () => {
    navigate(`/ticketing/${organizationId}/comp-tickets/${ticketedEventId}`);
  };

  const createActionListActions = () => {
    return [
      {
        text: formatMessage({ id: 'ticketing.home-page.event-action-dropdown.share' }),
        onClick: () => setIsShareModalOpen(true),
        qaId: `ticketed-event-item-share-action-${ticketedEventId}`,
      },
      {
        text: formatMessage({ id: 'ticketing.home-page.event-action-dropdown.reportCashSales' }),
        onClick: () => {
          navigate(buildReportCashSalesLink(organizationId!, ticketedEventId));
        },
        qaId: `ticketed-event-item-report-cash-sales-action-${ticketedEventId}`,
      },
      {
        text: formatMessage({ id: 'ticketing.home-page.event-action-dropdown.edit' }),
        onClick: () => {
          const scheduleEntryId = ticketedEvent.linkedEntries?.find(
            (le) => le.type === LinkedEntryType.HudlScheduleEntry
          )?.id;
          selectedFormStep(lastStep);
          navigate(`/ticketing/${organizationId}/${buildAddTicketingLink(ticketedEventId, scheduleEntryId)}`);
        },
        qaId: `ticketed-event-item-edit-action-${ticketedEventId}`,
      },
    ];
  };

  const renderHeader = () => {
    return (
      <div className={styles.headerContainer}>
        <div className={styles.headerNameContainer}>
          <Button buttonType="subtle" qaId="back-button" icon={<IconUiNavigationBack />} onClick={navigateBack} />
          <Headline level="1" qaId="ticketed-event-details-headline" className={styles.headerEventName}>
            {ticketedEvent.name}
          </Headline>
        </div>
        {!isPastEvent && (
          <div className={styles.headerActionContainer}>
            <div className={styles.headerButtons}>
              <Button
                className={styles.compTicketButtonContainer}
                buttonType="secondary"
                qaId="send-comp-tickets"
                icon={<IconTicket />}
                onClick={navigateToCompTicketing}
              >
                {formatMessage({ id: 'ticketing.complimentary.nav-button.title-tickets' })}
              </Button>
              <ActionList
                buttonIcon={<IconUiMore />}
                type="secondary"
                qaId="ticketed-event-details-action-list-btn"
                actions={createActionListActions()}
              />
            </div>
          </div>
        )}
      </div>
    );
  };

  const ticketedEventDetailsContainerTabs: TabProps[] = [
    {
      index: TicketedEventDetailsContainerTabs.Overview,
      title: formatMessage({ id: 'ticketing.tabs.overview' }),
      isSelected: selectedTab === TicketedEventDetailsContainerTabs.Overview,
    },
    {
      index: TicketedEventDetailsContainerTabs.PurchaserList,
      title: formatMessage({ id: 'ticketing.tabs.purchaser-list' }),
      isSelected: selectedTab === TicketedEventDetailsContainerTabs.PurchaserList,
    },
    {
      index: TicketedEventDetailsContainerTabs.ComplimentaryTickets,
      title: formatMessage({ id: 'ticketing.complimentary.totals-tickets' }),
      isSelected: selectedTab === TicketedEventDetailsContainerTabs.ComplimentaryTickets,
    },
  ];

  const onCompletedTicketGroupsFetch = (fetchedTicketGroups: TicketGroup[]) => {
    setTicketGroups(fetchedTicketGroups);
    setTicketGroupsLastUpdated(new Date());
  };

  const getAllFormFieldsInput: TicketGroupsForTicketedEventsInput = {
    eventIds: [ticketedEventId!],
    sortByAscending: true,
    sortType: TicketGroupSortType.TICKET_GROUP_PURCHASER_LAST_NAME,
  };

  const { ticketGroupsLoading, ticketGroupsError } = useGetAllTicketGroupsForTicketedEvent(
    getAllFormFieldsInput,
    !ticketedEventId,
    onCompletedTicketGroupsFetch
  );

  const { complimentaryTicketGroups, nonComplimentaryTicketGroups } = splitComplimentaryTicketGroups(ticketGroups);
  const isEventUpcoming = useMemo(() => isTicketedEventUpcoming(ticketedEvent), [ticketedEvent]);

  const renderTabContent = () => {
    switch (selectedTab) {
      case TicketedEventDetailsContainerTabs.Overview:
        return (
          <TicketedEventDetailsOverview
            ticketedEvent={ticketedEvent}
            organizationId={organizationId}
            lastUpdated={eventDetailsLastUpdated}
          />
        );
      case TicketedEventDetailsContainerTabs.PurchaserList:
        return (
          <PurchaserList
            ticketGroups={nonComplimentaryTicketGroups}
            ticketedEvent={ticketedEvent}
            referenceId={ticketedEvent.id}
            loading={ticketGroupsLoading}
            error={ticketGroupsError}
            lastUpdated={ticketGroupsLastUpdated}
            type={ItemType.TicketedEvent}
            formFields={ticketedEvent.formFields}
            emptyState={{
              header: formatMessage({ id: 'ticketing.purchaser-list.empty-state-headline-tickets' }),
              subHeader: isEventUpcoming
                ? formatMessage({ id: 'ticketing.purchaser-list.empty-state-text-tickets' })
                : '',
            }}
          />
        );
      case TicketedEventDetailsContainerTabs.ComplimentaryTickets:
        return (
          <PurchaserList
            ticketGroups={complimentaryTicketGroups}
            referenceId={ticketedEvent.id}
            ticketedEvent={ticketedEvent}
            loading={ticketGroupsLoading}
            error={ticketGroupsError}
            lastUpdated={ticketGroupsLastUpdated}
            type={ItemType.TicketedEvent}
            formFields={ticketedEvent.formFields}
            emptyState={{
              header: formatMessage({ id: 'ticketing.purchaser-list.empty-state-headline-comp-tickets' }),
              subHeader: isEventUpcoming
                ? formatMessage({ id: 'ticketing.purchaser-list.empty-state-text-comp-tickets' })
                : '',
            }}
          />
        );
      default:
        return assertNever(selectedTab);
    }
  };

  if (ticketedEventLoading) return renderLoading();
  if (ticketedEventError) return renderError();

  return (
    <div className={styles.ticketedEventDetailsContainer} data-qa-id="ticketed-event-details-container">
      <div className={styles.ticketedEventDetailsContent}>
        {renderHeader()}
        <div className={styles.tabsContainer}>
          <TabSwitcher
            selectedTab={selectedTab}
            tabs={ticketedEventDetailsContainerTabs}
            setSelectedTabReactiveVar={selectedTicketedEventDetailsContainerTab}
          />
        </div>
        {renderTabContent()}
      </div>
      {!isPastEvent ? (
        <ShareLinkModal
          isOpen={isShareModalOpen}
          qaId={`share-event-modal-${ticketedEvent.id}`}
          closeModal={() => setIsShareModalOpen(false)}
          shareLinkUrl={getOrderUrl(ticketedEvent.id, ItemType.TicketedEvent)}
          headerText={formatMessage({ id: 'ticketing.home-page.share-event' })}
          instructionsText={formatMessage({ id: 'ticketing.home-page.share-event.instructions' })}
        />
      ) : null}
    </div>
  );
}

export default TicketedEventDetailsContainer;
