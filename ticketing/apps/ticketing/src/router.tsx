import { createBrowserRouter, Outlet, RouteObject } from 'react-router-dom';

import { awaitPreloaders } from 'frontends-shared';

import { App } from './App';
import OrganizationAuthGuard from './components/OrganizationAuthGuard/OrganizationAuthGuard';
import OrgTicketingEnabledGuard from './components/OrgTicketingEnabledGuard/OrgTicketingEnabledGuard';
import { RouterError } from './components/RouterError';
import { ApolloWrapper } from './graphql/apolloWrapper';
import { HudlAuthProvider } from './graphql/hudlAuthContext';
import AddTicketingPage from './pages/AddTicketingPage/AddTicketingPage';
import CompPassesPage from './pages/CompPassesPage/CompPassesPage';
import CompTicketingPage from './pages/CompTicketingPage/CompTicketingPage';
import PassDetailsPage from './pages/PassDetailsPage/PassDetailsPage';
import PassPage from './pages/PassPage/PassPage';
import ReportCashSalesPage from './pages/ReportCashSalesPage/ReportCashSalesPage';
import TicketedEventDetailsPage from './pages/TicketedEventDetailsPage/TicketedEventDetailsPage';
import TicketingManagementHomePage from './pages/TicketingManagementHomePage/TicketingManagementHomePage';

const routerConfig: RouteObject[] = [
  {
    path: '/ticketing',
    errorElement: <RouterError />,
    loader: awaitPreloaders,
    element: (
      <HudlAuthProvider>
        <ApolloWrapper>
          <App />
        </ApolloWrapper>
      </HudlAuthProvider>
    ),
    children: [
      {
        path: ':organizationId',
        element: (
          <OrganizationAuthGuard>
            <OrgTicketingEnabledGuard>
              <Outlet />
            </OrgTicketingEnabledGuard>
          </OrganizationAuthGuard>
        ),
        children: [
          { index: true, element: <TicketingManagementHomePage /> },
          { path: 'add-ticketing/:scheduleEntryId', element: <AddTicketingPage /> },
          { path: 'add-ticketing', element: <AddTicketingPage /> },
          { path: 'create-pass', element: <PassPage /> },
          { path: 'pass/:passConfigId', element: <PassDetailsPage /> },
          { path: 'event/:ticketedEventId', element: <TicketedEventDetailsPage /> },
          { path: 'comp-tickets/:ticketedEventId', element: <CompTicketingPage /> },
          { path: 'comp-passes/:passConfigId', element: <CompPassesPage /> },
          { path: 'report-cash-sales/:ticketedEventId', element: <ReportCashSalesPage /> },
        ],
      },
    ],
  },
];

export const router = createBrowserRouter(routerConfig);
