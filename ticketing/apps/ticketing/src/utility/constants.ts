import { TicketingManagementTabs } from '../enums/tabEnums';
import { TicketType, TicketTypeReference } from '../graphql/generated/graphqlTypes';
import { DefaultTicketTypeWithTeams, SearchParams, SearchParamValue } from '../types/shared';

export const loggingParams = {
  func: {
    view: 'View',
    use: 'Use',
    ignore: 'Ignore',
    create: 'Create',
    cancel: 'Cancel',
    publishEventButtonClicked: 'PublishEventButtonClicked',
    publishEventSuccess: 'PublishEventSuccess',
    publishEventFailure: 'PublishEventFailure',
    ticketTypeSuccess: 'TicketTypeSuccess',
    ticketTypeFailure: 'TicketTypeFailure',
    getAccountStatusFailure: 'GetAccountStatusFailure',
    publishPassButtonClicked: 'PublishPassButtonClicked',
    publishPassSuccess: 'PublishPassSuccess',
    publishPassFailure: 'PublishPassFailure',
    getTicketGroupsByTicketedEventsWarning: 'GetTicketGroupsByTicketedEventsWarning',
    getTicketGroupsByPassConfigsWarning: 'GetTicketGroupsByPassConfigsWarning',
    publishCreateCompTicketSuccess: 'PublishCreateCompTicketSuccess',
    publishCreateCompTicketFail: 'PublishCreateCompTicketFail',
  },
  op: {
    compTicketing: 'CompTicketing',
    ticketedEvent: 'TicketedEvent',
    ticketType: 'TicketType',
    organizationIsFreeTicketingOnly: 'OrganizationIsFreeTicketingOnly',
    ticketingPassesEnabled: 'TicketingPassesEnabled',
    organizationPaymentPlatformStatus: 'OrganizationPaymentPlatformStatus',
    organizationHasAcceptedTermsOfService: 'OrganizationHasAcceptedTermsOfService',
    passConfig: 'PassConfig',
    reportCashSales: 'ReportCashSales',
  },
  page: {
    addTicketingPage: 'AddTicketingPage',
    ticketedEventDetailsPage: 'TicketedEventDetailsPage',
    ticketingManagementHomePage: 'TicketingManagementHomePage',
    addPassPage: 'AddPassPage',
    compTicketingPage: 'CompTicketingPage',
    reportCashSalesPage: 'ReportCashSalesPage',
  },
};

export const emptyTicketType: TicketType & TicketTypeReference = {
  id: '',
  name: '',
  organizationId: '',
  createdAt: undefined,
  priceInCents: 0,
  updatedAt: undefined,
};

export const dollarCentsConversion = 100;
export const formTitleMaxLength = 150;
export const formDescriptionMaxLength = 2000;

export const keyDownEvent = {
  key: 'ArrowDown',
};

export const initialSearchParams: SearchParams = {
  firstName: '',
  lastName: '',
  email: '',
  startDate: '',
  endDate: '',
};

export const initialSearchParamValues: SearchParamValue[] = [
  {
    label: 'First Name',
    value: 'firstName',
  },
  {
    label: 'Last Name',
    value: 'lastName',
  },
  {
    label: 'Email',
    value: 'email',
  },
  {
    label: 'Creation Start Date',
    value: 'startDate',
  },
  {
    label: 'Creation End Date',
    value: 'endDate',
  },
];

export const ticketedEventIdValue = 'ticketedEventId';
export const passConfigIdValue = 'passConfigId';

export const Routes = {
  AddTicketing: 'add-ticketing',
  ReportCashSales: 'report-cash-sales',
};

export const usIanaTimeZones = [
  'Pacific/Honolulu',
  'America/Juneau',
  'America/Los_Angeles',
  'America/Phoenix',
  'America/Boise',
  'America/Chicago',
  'America/Detroit',
];

export const caIanaTimeZones = [
  'America/Vancouver',
  'America/Whitehorse',
  'America/Edmonton',
  'America/Regina',
  'America/Toronto',
  'America/Halifax',
  'America/St_Johns',
];

export const CountryIsoAlpha2Codes = {
  US: 'US',
  CA: 'CA',
};

export const TicketedEventTabs = [
  TicketingManagementTabs.PublishedTicketedEvents,
  TicketingManagementTabs.NonTicketedEvents,
  TicketingManagementTabs.DraftTicketedEvents,
  TicketingManagementTabs.PastTicketedEvents,
];

export const PassesTabs = [TicketingManagementTabs.ActivePasses, TicketingManagementTabs.DraftPasses];

// This date is used as the lower bound (start date) when fetching schedule entries for ticketed events.
// No ticketed events should have schedule entries before this date.
export const ticketingInceptionDate = '2023-01-01T01:00:00.000Z';
export const scheduleEntrySummariesFilterEndDateYears = 15;

export const quantityInputMaxLength = 9;

export const reservedSeatDescriptors = {
  section: 'Section',
  generalAdmissionArea: 'Zone',
  table: 'Table',
  row: 'Row',
  seat: 'Seat',
};

export const supportURL = 'https://www.hudl.com/support/contact';

export const reservedSeatingMaxRecipients = 100;

export const reservedSeatingSessionTimeoutInSeconds = 60 * 15;
export const reservedSeatingSessionTimeoutWarningInSeconds = 60 * 3;

export const minimumTicketPriceForBundledFeesInCents = 200;
export const minimumPassPriceForBundledFeesInCents = 400;

export const OtherSport = 'othersport';

export const SCHEDULE_REFERENCE_UPLOADER_ALLOWED_FILE_TYPES = [
  'pdf',
  'doc',
  'docx',
  'odt',
  'rtf',
  'csv',
  'xlsx',
  'xls',
  'jpg',
  'jpeg',
  'png',
  'gif',
  'bmp',
  'txt',
];

export const SCHEDULE_REFERENCE_UPLOADER_MAX_FILE_SIZE_IN_BYTES = 10000000;
export const SCHEDULE_REFERENCE_UPLOADER_MAX_FILE_COUNT = 10;
export const SCHEDULE_REFERENCE_UPLOADER_NOTES_CHARACTER_LIMIT = 2500;

export const emptyTeamTicketType: DefaultTicketTypeWithTeams = {
  name: '',
  priceInCents: 0,
  teamIds: [],
};

export const MaxMobileWidthInt = 480;
export const MaxTabletWidthInt = 769;
export const MaxMobileWidth = `${MaxMobileWidthInt}px`;
export const MaxTabletWidth = `${MaxTabletWidthInt}px`;
