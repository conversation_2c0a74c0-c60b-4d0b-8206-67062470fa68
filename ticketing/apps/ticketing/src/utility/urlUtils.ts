import { getMarvelOrigin } from '@hudl/frontends-environment';

import { ItemType } from '../enums/shared';
import { Routes, ticketedEventIdValue } from './constants';

enum Environment {
  Thor = 'thorhudl',
  Prod = 'hudl',
}

export const getOrderUrl = (referenceId: string, itemType: ItemType): string => {
  const { hostname } = window.location;
  const hostParts = hostname.replace('admin.', '').split('.');
  const domain = hostParts[hostParts.length - 2];
  const type = itemType === ItemType.PassConfig ? 'passes' : 'tickets';
  if (domain === Environment.Prod) {
    return `https://fan.${domain}.com/${type}/${referenceId}`;
  }

  const branch = hostParts[0];
  return `https://${branch}.fan.${domain}.com/${type}/${referenceId}`;
};

export const getBundledFeesUrl = () => {
  return 'http://support.hudl.com/s/article/set-up-bundled-fees';
};

export const buildAddTicketingLink = (ticketedEventId: string | undefined, scheduleEntryId: string | undefined) => {
  const baseUrl = scheduleEntryId ? `${Routes.AddTicketing}/${scheduleEntryId}` : Routes.AddTicketing;
  const queryParams = ticketedEventId && new URLSearchParams([[ticketedEventIdValue, ticketedEventId]]);
  return ticketedEventId ? `${baseUrl}?${queryParams?.toString()}` : baseUrl;
};

export const buildReportCashSalesLink = (organizationId: string, ticketedEventId: string | undefined) => {
  return `/ticketing/${organizationId}/${Routes.ReportCashSales}/${ticketedEventId}`;
};

export const buildTicketedEventDetailsLink = (organizationId: string, ticketedEventId: string) => {
  return `/ticketing/${organizationId}/event/${ticketedEventId}`;
};

export const getScheduleReferenceUploaderUploadUrl = () => {
  return `${getMarvelOrigin()}/app/schedules/schedule-reference-file/upload-schedule-reference-file`;
};
