import { TicketedEvent as SharedTicketedEvent, TicketedEventStatus } from 'ticketing-shared';

import { EventStatus, ScheduleEntryLocation } from '../enums/shared';
import { ScheduleEntryPublicSummary, TicketedEvent } from '../graphql/generated/graphqlTypes';

export const isScheduleEntryForTicketedEvent = (
  scheduleEntry: ScheduleEntryPublicSummary,
  ticketedEvent: TicketedEvent
) => {
  return ticketedEvent.linkedEntries?.some(
    (l) =>
      scheduleEntry.scheduleEntryLocation === ScheduleEntryLocation.Home &&
      l.type === 'HudlScheduleEntry' &&
      l.id === scheduleEntry.scheduleEntryId
  );
};

export const isTicketedEventUpcoming = (ticketedEvent: TicketedEvent | undefined) => {
  if (!ticketedEvent) {
    return false;
  }

  return ticketedEvent.eventStatus === EventStatus.Upcoming;
};

export const ConvertToSharedTicketedEvent = (ticketedEvent: TicketedEvent | undefined): SharedTicketedEvent => {
  return ticketedEvent
    ? ({
        id: ticketedEvent.id ?? '',
        name: ticketedEvent.name ?? '',
        description: ticketedEvent.description ?? '',
        gender: ticketedEvent.gender ?? '',
        eventStatus: (ticketedEvent.eventStatus ?? '') as TicketedEventStatus,
        date: ticketedEvent.date ?? '',
        ticketTypes: ticketedEvent.ticketTypes ?? [],
        ticketTypeReferences: [],
        venueId: ticketedEvent.venueId ?? '',
        visibility: ticketedEvent.visibility ?? '',
        participatingTeamIds: [],
        feeStrategy: ticketedEvent.feeStrategy ?? '',
        formFieldIds: ticketedEvent.formFieldIds ?? [],
        createdAt: ticketedEvent.createdAt ?? '',
        organizationId: ticketedEvent.organizationId ?? '',
        ticketCount: ticketedEvent.ticketCount ?? 0,
        updatedAt: ticketedEvent.updatedAt ?? '',
      } as SharedTicketedEvent)
    : undefined;
};
