{"name": "@hudl/video-playback-react-native", "version": "1.4.0", "contributors": [{"name": "Truckers", "url": "https://hudl.slack.com/archives/C03FP4LBYFN", "channel": "#squad-truckers"}], "sideEffects": false, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"android:emulators": "cd .storybook-native && pnpm jarvis emulator --list", "android:open": "cd .storybook-native && pnpm jarvis emulator", "build": "vite build", "build-storybook": "storybook build --stats-json", "chromatic": "chromatic --exit-once-uploaded --storybook-build-dir storybook-static --storybook-base-dir video/packages/video-playback-react-native --project-token $VIDEO_PLAYBACK_REACT_NATIVE_CHROMATIC_PROJECT_TOKEN", "chromatic:skip": "chromatic --skip --project-token $VIDEO_PLAYBACK_REACT_NATIVE_CHROMATIC_PROJECT_TOKEN", "clean": "rimraf dist node_modules/.cache storybook-static", "dev": "vite build --watch", "lint": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet", "lint:fix": "eslint --ext .js,.ts,.jsx,.tsx src/ --quiet --fix", "nuke": "pnpm run clean && rimraf node_modules && rimraf .storybook-native", "prettier": "prettier --check \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "prettier:fix": "prettier --write \"./**/*.{html,js,json,jsx,md,mdx,ts,tsx,yaml,yml}\" --config ../../../prettier.config.mjs --ignore-path ../../../.prettierignore", "release": "release-package", "storybook": "storybook dev -p 9001", "storybook:native:android": "cd .storybook-native && pnpm android", "storybook:native:generate": "generate-storybook-native", "storybook:native:ios": "cd .storybook-native && pnpm ios", "storybook:native:start": "cd .storybook-native && pnpm start", "test": "vitest", "test:ci": "vitest --no-cache --silent", "test:nowatch": "vitest --watch=false", "test:update": "vitest --no-cache --update", "types": "tsc --project tsconfig-declarations.json --sourceRoot $PWD/src", "types:check": "tsc --noEmit --sourceRoot $PWD/src", "types:watch": "tsc --noEmit --pretty --watch --sourceRoot $PWD/src"}, "dependencies": {"@hudl/rn-uniform": "^4.45.0", "@hudl/uniform-tokens": "workspace:*", "@miblanchard/react-native-slider": "2.6.0", "@theoplayer/react-native-analytics-youbora": "1.3.0", "@theoplayer/react-native-ui": "0.13.0", "react-native-theoplayer": "^9.8.0", "ua-parser-js": "^2.0.3"}, "devDependencies": {"@hudl/eslint-config": "workspace:*", "@hudl/react-native-config": "workspace:*", "@hudl/vite-config": "workspace:*", "@hudl/vitest-config": "workspace:*", "config": "workspace:*", "eslint": "8.45.0", "react-native-safe-area-context": "5.4.1", "react-native-svg": "15.11.2", "theoplayer": "9.7.0", "vite": "5.4.7", "vite-tsconfig-paths": "4.3.2"}, "peerDependencies": {"react": "*", "react-native": "*", "react-native-google-cast": "*", "react-native-safe-area-context": "^5", "react-native-svg": "^15", "react-native-web": "*", "theoplayer": "^9"}, "peerDependenciesMeta": {"react-native-google-cast": {"optional": true}, "react-native-svg": {"optional": true}, "react-native-web": {"optional": true}, "theoplayer": {"optional": true}}}