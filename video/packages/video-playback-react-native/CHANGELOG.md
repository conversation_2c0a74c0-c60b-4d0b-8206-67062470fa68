# Changelog

All notable changes to the Hudl Video Playback (React Native) library will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/), and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.4.0] - 2025-07-18

### Added

- Create `useOrientation` to track current orientation of device
- Exported ActionButton, ChromecastSvg, SettingsSvg for use outside of this package

## [1.3.0] - 2025-07-16

### Changed

- Replaced existing playback speed and video quality settings with new custom popover components matching the latest design.
  - <sub><sup>**Some assembly required.** (see `fan-video-player`)</sup></sub>

## [1.2.0] - 2025-07-11

### Changed

- Fixes some player sizing issues on native
- Implements new version of usePresentationMode that correctly keeps track of player's viewing mode (fullscreen, inline, pip, etc.)

## [1.1.0] - 2025-06-13

### Added

- Initial UI

## [1.0.0] - 2025-05-09

### Added

- Initial release.
