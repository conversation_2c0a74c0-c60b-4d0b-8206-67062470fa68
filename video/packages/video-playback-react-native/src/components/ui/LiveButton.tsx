import React, { useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity } from 'react-native';

import { PlayerContext, useCurrentTime, useDuration, useSeekable } from '@theoplayer/react-native-ui';

import { ColorsDark } from '@hudl/rn-uniform';

import { NEAR_LIVE_THRESHOLD_MS } from '../../constants';
import DotIcon from './DotIcon';

const liveStyles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
    width: 55,
  },
  text: {
    color: ColorsDark.buttonLabelWhite,
    fontSize: 12,
    fontWeight: 700,
  },
});

export interface LiveButtonProps {
  testID?: string;
  /**
   * Configurable time in milliseconds to determine if the player is "near live".
   * Defaults to 15 seconds.
   */
  nearLiveThresholdMs?: number;
}

export function LiveButton({
  testID,
  nearLiveThresholdMs = NEAR_LIVE_THRESHOLD_MS,
}: LiveButtonProps): React.JSX.Element {
  const { player, locale } = React.useContext(PlayerContext);
  const currentTime = useCurrentTime();
  const duration = useDuration();
  const seekable = useSeekable();
  const normalizedDuration = isNaN(duration) || !isFinite(duration) ? 0 : Math.max(0, duration);
  const seekableEnd = seekable.length > 0 ? seekable[0].end : normalizedDuration;
  const isNearLive = Math.abs(currentTime - seekableEnd) <= nearLiveThresholdMs;

  const onPress = useCallback(() => {
    player.currentTime = seekableEnd;
  }, [player, seekableEnd]);

  return (
    <TouchableOpacity style={liveStyles.container} onPress={onPress} testID={testID}>
      <DotIcon color={isNearLive ? 'critical' : 'nonessential'} style={{ width: 8, height: 8, marginRight: 4 }} />
      <Text style={liveStyles.text}>{locale.liveLabel}</Text>
    </TouchableOpacity>
  );
}
