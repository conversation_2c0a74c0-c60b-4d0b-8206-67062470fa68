import { useCallback, useContext, useState } from 'react';
import { Platform, StyleSheet, TouchableOpacity, View } from 'react-native';

import {
  PlayerContext,
  SvgContext,
  type ActionButtonProps as THEOActionButtonProps,
} from '@theoplayer/react-native-ui';

import PlayerText from './PlayerText';

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  icon: {
    height: '100%',
    aspectRatio: 1,
    padding: 5,
    flex: 1,
  },
  afterText: {
    flex: 1,
    paddingRight: 5,
  },
});

export interface ActionButtonProps extends Omit<THEOActionButtonProps, 'icon'> {
  afterText?: string;
}

/**
 * The default button component that renders an image/svg source for the `react-native-theoplayer` UI. This is largely a copy of THEO's implementation,
 * but it removes a single wrapper View that messed with some of our styling.
 */
export function ActionButton(props: ActionButtonProps): React.JSX.Element {
  const { style, svg, onPress, highlighted, testID, afterText } = props;
  const [focused, setFocused] = useState<boolean>(false);
  const context = useContext(PlayerContext);
  const shouldChangeTintColor = highlighted || (focused && Platform.isTV);
  const touchable = props.touchable != false;
  const { style: contextStyle } = useContext(PlayerContext);
  const iconStyle = style;

  const onTouch = useCallback(() => {
    if (context.ui.buttonsEnabled_) {
      onPress?.();
    }
    context.ui.onUserAction_();
  }, [context.ui, onPress]);

  const onFocus = useCallback(() => {
    context.ui.onUserAction_();
    setFocused(true);
  }, [context.ui]);

  const onBlur = useCallback(() => {
    setFocused(false);
  }, [setFocused]);

  if (!touchable) {
    return (
      <View style={styles.container} testID={testID}>
        {svg && <View style={[styles.icon, iconStyle]}>{svg}</View>}
        {afterText && <PlayerText style={styles.afterText}>{afterText}</PlayerText>}
      </View>
    );
  }

  return (
    <TouchableOpacity style={styles.container} testID={testID} onPress={onTouch} onFocus={onFocus} onBlur={onBlur}>
      {svg && (
        <View style={[styles.icon, iconStyle]}>
          <SvgContext.Provider
            value={{
              fill: shouldChangeTintColor ? contextStyle.colors.iconSelected : contextStyle.colors.icon,
              height: '100%',
              width: '100%',
            }}
          >
            {svg}
          </SvgContext.Provider>
        </View>
      )}
      {afterText && <PlayerText style={styles.afterText}>{afterText}</PlayerText>}
    </TouchableOpacity>
  );
}
