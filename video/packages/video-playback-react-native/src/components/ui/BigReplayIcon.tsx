import { Path, Rect } from 'react-native-svg';

import { Icon } from '@hudl/rn-uniform';

import type { UniformIconProps } from '../../types/UniformIconProps';

export type BigPlayIconProps = UniformIconProps;

export default function BigPlayIcon({ style, ...rest }: BigPlayIconProps): React.JSX.Element {
  return (
    <Icon width="81" height="80" viewBox="0 0 81 80" fill="none" style={style} {...rest}>
      <Rect x="0.779785" width="80" height="80" rx="40" fill="white" fillOpacity="0.2" />
      <Path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.0388 25.7163L35.0258 29.7283C34.6358 30.1183 34.6358 30.7523 35.0258 31.1423L39.0388 35.1553C39.3308 35.4473 39.8058 35.4473 40.0988 35.1553C40.2398 35.0143 40.3188 34.8243 40.3188 34.6253V32.4353C45.2888 32.4353 49.3188 36.4643 49.3188 41.4353C49.3188 46.4063 45.2888 50.4353 40.3188 50.4353C35.3488 50.4353 31.3188 46.4063 31.3188 41.4353C31.3188 41.0973 31.3368 40.7643 31.3738 40.4353H31.3188C31.3188 39.8833 30.8708 39.4353 30.3188 39.4353H28.3188C27.7668 39.4353 27.3188 39.8833 27.3188 40.4353C27.3188 40.5093 27.3268 40.5813 27.3418 40.6503C27.3268 40.9103 27.3188 41.1713 27.3188 41.4353C27.3188 48.6143 33.1388 54.4353 40.3188 54.4353C47.4988 54.4353 53.3188 48.6143 53.3188 41.4353C53.3188 34.2563 47.4988 28.4353 40.3188 28.4353V26.2463C40.3188 25.8323 39.9828 25.4963 39.5688 25.4963C39.3698 25.4963 39.1788 25.5743 39.0388 25.7163Z"
        fill="white"
      />
    </Icon>
  );
}
