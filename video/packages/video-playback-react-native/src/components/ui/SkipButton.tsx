import { type ReactNode, useCallback, useContext, useEffect, useRef, useState } from 'react';
import { Animated, Easing, type StyleProp, Text, TouchableOpacity, View, type ViewStyle } from 'react-native';
import { StyleSheet } from 'react-native';

import { ActionButton, PlayerContext, type SkipButtonProps as THEOSkipButtonProps } from '@theoplayer/react-native-ui';
import { PlayerEventType } from 'react-native-theoplayer';

import { IconBackward5Sec, IconForward5Sec } from '@hudl/rn-uniform';

const styles = StyleSheet.create({
  touchable: {
    padding: 0,
  },
  animatedView: {
    alignContent: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    justifyContent: 'center',
  },
});

export type SkipButtonProps = Omit<THEOSkipButtonProps, 'style'> & {
  touchableStyle?: StyleProp<ViewStyle>;
  actionButtonStyle?: StyleProp<ViewStyle>;
  animatedViewStyle?: StyleProp<ViewStyle>;
  textContainerStyle?: StyleProp<ViewStyle>;
  iconSkipStyle?: StyleProp<ViewStyle>;
  showSkipSecondsText?: boolean;
  initiallyEnabled?: boolean;
};

/**
 * The default skip button for the `react-native-theoplayer` UI. This is largely a copy of THEO's implementation,
 * but it has more styling props exposed so that we have more control over the styling.
 */
export function SkipButton({
  touchableStyle,
  animatedViewStyle,
  textContainerStyle,
  iconSkipStyle,
  showSkipSecondsText = false,
  initiallyEnabled = true,
  skip,
  rotate,
  icon,
  textStyle,
  testID,
}: SkipButtonProps): React.JSX.Element {
  const spinValue = useRef<Animated.Value>(new Animated.Value(0)).current;
  const { player, style: contextStyle } = useContext(PlayerContext);
  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: skip >= 0 ? ['0deg', '360deg'] : ['360deg', '0deg'],
  });

  const [enabled, setEnabled] = useState<boolean>(initiallyEnabled);
  useEffect(() => {
    const onUpdateEnabled = (): void => {
      setEnabled(player.seekable.length > 0 || player.buffered.length > 0);
    };
    const onPlaying = (): void => {
      const isCasting = player.cast.chromecast?.casting ?? false;
      setEnabled(player.seekable.length > 0 || player.buffered.length > 0 || isCasting);
    };
    player.addEventListener([PlayerEventType.PROGRESS, PlayerEventType.SOURCE_CHANGE], onUpdateEnabled);
    player.addEventListener(PlayerEventType.PLAYING, onPlaying);
    return () => {
      player.removeEventListener([PlayerEventType.PROGRESS, PlayerEventType.SOURCE_CHANGE], onUpdateEnabled);
      player.removeEventListener(PlayerEventType.PLAYING, onPlaying);
    };
  }, [player]);

  const onPress = useCallback(() => {
    player.currentTime = player.currentTime + skip * 1e3;
    if (rotate === true) {
      Animated.timing(spinValue, {
        toValue: 0.1,
        duration: 900,
        easing: Easing.linear,
        useNativeDriver: true,
      }).start(() => {
        Animated.timing(spinValue, {
          toValue: 0,
          duration: 100,
          easing: Easing.linear,
          useNativeDriver: true,
        }).start();
      });
    }
  }, [player, skip, rotate, spinValue]);

  const forwardSvg: ReactNode = icon?.forward ?? <IconForward5Sec style={iconSkipStyle} />;
  const backwardSvg: ReactNode = icon?.backward ?? <IconBackward5Sec style={iconSkipStyle} />;
  if (!enabled) {
    return <></>;
  }

  return (
    <Animated.View style={[styles.animatedView, animatedViewStyle, { transform: [{ rotate: spin }] }]}>
      <TouchableOpacity
        activeOpacity={rotate === true ? 1 : 0.2}
        style={[styles.touchable, touchableStyle]}
        testID={testID}
        onPress={onPress}
      >
        <ActionButton
          touchable={false}
          svg={skip < 0 ? backwardSvg : forwardSvg}
          style={[styles.touchable, touchableStyle]}
        />
        {showSkipSecondsText && (
          <View style={[styles.textContainer, textContainerStyle]}>
            <Text style={[contextStyle.text, { color: contextStyle.colors.text }, textStyle]}>{Math.abs(skip)}</Text>
          </View>
        )}
      </TouchableOpacity>
    </Animated.View>
  );
}
