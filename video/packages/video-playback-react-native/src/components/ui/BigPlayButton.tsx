import { StyleSheet } from 'react-native';

import { PlayButton, type PlayButtonProps } from '@theoplayer/react-native-ui';

import BigPlayIcon from './BigPlayIcon';
import BigReplayIcon from './BigReplayIcon';

export type BigPlayButtonProps = PlayButtonProps;

const styles = StyleSheet.create({
  icon: {
    width: 80,
    height: 80,
  },
  container: {
    width: '100%',
    height: '100%',
    padding: 0,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default function BigPlayButton(props: BigPlayButtonProps): React.JSX.Element {
  return (
    <PlayButton
      icon={{
        play: <BigPlayIcon style={styles.icon} />,
        pause: <></>,
        replay: <BigReplayIcon style={styles.icon} />,
      }}
      style={styles.container}
      {...props}
    />
  );
}
