import type { PropsWithChildren } from 'react';
import { View, type ViewProps } from 'react-native';
import { StyleSheet } from 'react-native';

import { UniformSpace } from '@hudl/rn-uniform';

import { BG_40_OPACITY } from '../../constants';

export type PanelProps = PropsWithChildren<ViewProps>;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-start',
    paddingHorizontal: UniformSpace.quarter,
    backgroundColor: BG_40_OPACITY,
    borderRadius: UniformSpace.half,
  },
});

export default function Panel({ style, children, ...rest }: PanelProps): React.JSX.Element {
  return (
    <View style={[styles.container, style]} {...rest} collapsable={false}>
      {children}
    </View>
  );
}
