import type { <PERSON>a, StoryObj } from '@storybook/react-vite';

import { ActionList } from '@hudl/uniform-web-actions';
import { type GridProps } from '@hudl/uniform-web-grid';

import GridStoriesMeta from './Grid.stories';
import { gridSampleDataConfig } from './Grid.storyutils';

export default {
  ...GridStoriesMeta,
  title: 'Chromatic Tests/Grid',
  parameters: {
    ...GridStoriesMeta.parameters,
    chromatic: { disableSnapshot: false },
    controls: { disable: true },
  },
} as Meta<typeof ActionList>;

export const Default: StoryObj<GridProps> = {
  args: {
    ...gridSampleDataConfig,
    rowSelection: { mode: 'multiRow' },
    selectionColumnDef: { pinned: 'left' },
  },
};
