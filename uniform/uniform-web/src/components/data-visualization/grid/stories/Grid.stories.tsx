import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

import { Grid, type GridProps } from '@hudl/uniform-web-grid';

import { gridSampleDataConfig } from './Grid.storyutils';

export default {
  component: Grid,
  title: 'Beta Components/Grid',
  args: {
    ...gridSampleDataConfig,
  },
  argTypes: {
    pagination: {
      control: { type: 'boolean' },
      description: 'Enable pagination for the grid',
      table: {
        defaultValue: { summary: 'false' },
        type: { summary: 'boolean' },
      },
    },
    paginationPageSize: {
      control: { type: 'number', min: 5, max: 50, step: 5 },
      description: 'Number of rows per page when pagination is enabled',
      table: {
        defaultValue: { summary: '100' },
        type: { summary: 'number' },
      },
    },
    rowNumbers: {
      control: 'boolean',
      description: 'Enable row numbers in the grid',
      table: {
        defaultValue: { summary: 'false' },
        type: { summary: 'boolean' },
      },
    },
    rowSelection: {
      control: 'select',
      options: ['none', 'singleRow', 'multiRow'],
      description: 'Type of row selection in the grid',
      table: {
        defaultValue: { summary: 'singleRow' },
        type: { summary: 'string' },
      },
    },
    suppressMovableColumns: {
      control: 'boolean',
      description: 'Disable the ability to move columns in the grid',
      table: {
        defaultValue: { summary: 'false' },
        type: { summary: 'boolean' },
      },
    },
    suppressRowVirtualisation: {
      control: 'boolean',
      description: 'Disable row virtualisation to render all rows at once',
      table: {
        defaultValue: { summary: 'false' },
        type: { summary: 'boolean' },
      },
    },
    defaultColDef: {
      control: 'select',
      options: ['Default', 'Hide Header Menu & Filter'],
      mapping: {
        Default: {
          sortable: true,
          filter: true,
          resizable: true,
          editable: true,
          suppressHeaderMenuButton: false,
          suppressHeaderFilterButton: false,
        },
        'Hide Header Menu & Filter': {
          sortable: true,
          filter: true,
          resizable: true,
          editable: true,
          suppressHeaderMenuButton: true,
          suppressHeaderFilterButton: true,
        },
      },
      description: 'Default column definition for the grid',
      table: {
        type: { summary: 'object' },
      },
    },
    selectionColumnDef: {
      control: 'object',
      description: 'Column definition for the selection column',
      table: {
        type: { summary: 'object' },
      },
    },
  },
} as Meta<GridProps>;

export const Demo: StoryObj<GridProps> = {
  render: (args) => {
    return <Grid {...args} key={JSON.stringify(args.rowNumbers)} />;
  },
  args: {
    rowSelection: { mode: 'multiRow' },
    selectionColumnDef: { pinned: 'left' },
    pagination: false,
    paginationPageSize: 20,
    rowNumbers: false,
    suppressMovableColumns: true,
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      editable: true,
      suppressHeaderMenuButton: false,
      suppressHeaderFilterButton: false,
    },
    suppressRowVirtualisation: false,
  },
};
