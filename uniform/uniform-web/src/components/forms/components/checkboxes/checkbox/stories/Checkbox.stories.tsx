import type { Meta, StoryObj } from '@storybook/react-vite';

import { Checkbox, type CheckboxProps } from '../../../../../../index';

export default {
  component: Checkbox,
  title: 'Forms/Checkbox',
  argTypes: {
    // text
    value: {
      control: 'text',
    },
  },
  args: {
    // default
    displayType: 'default',
  },
} as Meta<CheckboxProps>;

export const Demo: StoryObj<CheckboxProps> = {
  args: {
    displayType: 'default',
    isChecked: false,
    isDisabled: false,
    isReadOnly: false,
    isPartiallyChecked: false,
    label: 'Checkbox label',
    value: 'checkbox',
    qaId: 'checkbox-test',
    className: '',
  },

  argTypes: {
    onChange: {
      action: 'onChange',
    },
  },
};
